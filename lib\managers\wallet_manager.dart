import 'package:get/get.dart';
import 'package:flutter/foundation.dart';

import '../controllers/wallet_controller.dart';
import '../models/transaction_model.dart';
import '../models/wallet_state.dart' as ws;

/// Legacy WalletManager that now delegates to GetX WalletController
/// This maintains backward compatibility while using GetX state management
class WalletManager extends ChangeNotifier {
  static final WalletManager _instance = WalletManager._internal();
  factory WalletManager() => _instance;
  WalletManager._internal();

  WalletController get _walletController => Get.find<WalletController>();

  double get balance => _walletController.balance;
  bool get isInitialized => _walletController.isInitialized;
  bool get isLoading => _walletController.isLoading;
  List<TransactionModel> get transactions => _walletController.transactions;

  // Create a compatible state object for backward compatibility
  ws.WalletState get state => ws.WalletState(
    balance: _walletController.balance,
    transactions: _walletController.transactions,
    status: _convertStatus(_walletController.status),
    errorMessage: _walletController.errorMessage,
    lastUpdated: _walletController.lastUpdated,
    isLoading: _walletController.isLoading,
    totalEarnings: _walletController.totalEarnings,
    totalSpent: _walletController.totalSpent,
  );

  ws.WalletStatus _convertStatus(WalletStatus status) {
    switch (status) {
      case WalletStatus.uninitialized:
        return ws.WalletStatus.uninitialized;
      case WalletStatus.initializing:
        return ws.WalletStatus.initializing;
      case WalletStatus.ready:
        return ws.WalletStatus.ready;
      case WalletStatus.error:
        return ws.WalletStatus.error;
      case WalletStatus.syncing:
        return ws.WalletStatus.syncing;
    }
  }

  Future<void> initialize() async {
    await _walletController.initialize();
  }

  Future<bool> addFunds(double amount, {String? paymentMethodId}) async {
    return await _walletController.addFunds(
      amount,
      paymentMethodId: paymentMethodId,
    );
  }

  Future<bool> deductFunds({
    required double amount,
    required String description,
    String? postId,
  }) async {
    return await _walletController.deductFunds(
      amount: amount,
      description: description,
      postId: postId,
    );
  }

  String formatCurrency(double amount) {
    return _walletController.formatCurrency(amount);
  }

  void clearError() {
    _walletController.clearError();
  }

  @override
  void dispose() {
    // GetX controller handles its own disposal
    super.dispose();
  }
}
