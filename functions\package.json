{"name": "money-mouthy-functions", "description": "Simple Firebase Functions for Money Mouthy App - Stripe Payment Integration", "scripts": {"serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "index.js", "dependencies": {"firebase-admin": "^12.7.0", "firebase-functions": "^4.8.0", "stripe": "^14.15.0"}, "private": true}