import 'package:flutter/material.dart' show Colors, Icons;
import 'package:flutter/material.dart';

/// Category data model
class CategoryData {
  final String name;
  final Color color;
  final double topPrice;
  final IconData icon;

  const CategoryData({
    required this.name,
    required this.color,
    required this.icon,
    this.topPrice = 0.0,
  });

  /// Convert to Map for compatibility with existing code
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'color': color,
      'icon': icon,
    };
  }

  /// Create from Map for compatibility
  static CategoryData fromMap(Map<String, dynamic> map) {
    return CategoryData(
      name: map['name'] as String,
      color: map['color'] as Color,
      icon: map['icon'] as IconData,
      topPrice: (map['topPrice'] as double?) ?? 0.0,
    );
  }
}

/// Single source of truth for all categories
/// This is the ONLY place where categories should be defined
class Categories {
  static const List<CategoryData> all = [
    CategoryData(
      name: 'News',
      color: Color(0xFF29CC76),
      icon: Icons.newspaper,
    ),
    CategoryData(
      name: 'Politics',
      color: Color(0xFF4C5DFF),
      icon: Icons.how_to_vote,
    ),
    CategoryData(
      name: 'Sex',
      color: Color(0xFFFF4081),
      icon: Icons.favorite,
    ),
    CategoryData(
      name: 'Entertainment',
      color: Color(0xFFA06A00),
      icon: Icons.movie,
    ),
    CategoryData(
      name: 'Sports',
      color: Color(0xFFC43DFF),
      icon: Icons.sports_soccer,
    ),
    CategoryData(
      name: 'Religion',
      color: Color(0xFF000000),
      icon: Icons.church,
    ),
  ];

  /// Get category names only (for dropdowns, etc.)
  static List<String> get names => all.map((cat) => cat.name).toList();

  /// Get category names with 'All' option for filtering
  static List<String> get namesWithAll => ['All', ...names];

  /// Find category by name
  static CategoryData? findByName(String name) {
    try {
      return all.firstWhere((cat) => cat.name == name);
    } catch (e) {
      return null;
    }
  }

  /// Get category index by name (returns -1 if not found)
  static int getIndexByName(String name) {
    for (int i = 0; i < all.length; i++) {
      if (all[i].name == name) {
        return i;
      }
    }
    return -1;
  }

  /// Get category name by index (returns null if invalid index)
  static String? getNameByIndex(int index) {
    if (index >= 0 && index < all.length) {
      return all[index].name;
    }
    return null;
  }

  /// Get category color by name
  static Color? getColorByName(String name) {
    final category = findByName(name);
    return category?.color;
  }

  /// Get category icon by name
  static IconData? getIconByName(String name) {
    final category = findByName(name);
    return category?.icon;
  }

  /// Default category name
  static const String defaultCategory = 'News';

  /// Validate if a category name exists
  static bool isValidCategory(String name) {
    return findByName(name) != null;
  }

  /// Get legacy format for backward compatibility
  static List<Map<String, dynamic>> get legacyFormat {
    return all.map((cat) => cat.toMap()).toList();
  }
}

/// Category Badge Widget
class CategoryBadge extends StatelessWidget {
  final CategoryData category;

  const CategoryBadge({super.key, required this.category});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: category.color,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        category.name,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
