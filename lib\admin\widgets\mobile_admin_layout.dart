import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../theme/admin_theme.dart';
import '../controllers/admin_auth_controller.dart';
import 'admin_bottom_sheet.dart';

class MobileAdminLayout extends StatefulWidget {
  final Widget child;
  final String title;
  final Widget? floatingActionButton;
  final List<Widget>? actions;

  const MobileAdminLayout({
    super.key,
    required this.child,
    required this.title,
    this.floatingActionButton,
    this.actions,
  });

  @override
  State<MobileAdminLayout> createState() => _MobileAdminLayoutState();
}

class _MobileAdminLayoutState extends State<MobileAdminLayout> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final adminAuthController = Get.find<AdminAuthController>();
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;

    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: AdminTheme.backgroundColor,
      appBar: _buildAppBar(context, isMobile),
      drawer: isMobile ? _buildMobileDrawer(context) : null,
      body: _buildBody(context, isMobile),
      floatingActionButton: widget.floatingActionButton,
      bottomNavigationBar: isMobile ? _buildBottomNavigation(context) : null,
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context, bool isMobile) {
    return AppBar(
      title: Text(
        widget.title,
        style: AdminTheme.headingSmall.copyWith(
          color: AdminTheme.textOnPrimary,
        ),
      ),
      backgroundColor: AdminTheme.primaryColor,
      foregroundColor: AdminTheme.textOnPrimary,
      elevation: 0,
      actions: [
        if (widget.actions != null) ...widget.actions!,
        if (!isMobile) ...[
          // Profile section for desktop
          Obx(() => Padding(
                padding: const EdgeInsets.only(right: AdminTheme.spacingMedium),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircleAvatar(
                      backgroundColor: AdminTheme.textOnPrimary,
                      radius: 16,
                      child: Text(
                        adminAuthController.currentAdmin?.name
                                .substring(0, 1)
                                .toUpperCase() ??
                            'A',
                        style: AdminTheme.labelMedium.copyWith(
                          color: AdminTheme.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const SizedBox(width: AdminTheme.spacingSmall),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          adminAuthController.currentAdmin?.name ?? 'Admin',
                          style: AdminTheme.bodySmall.copyWith(
                            color: AdminTheme.textOnPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          adminAuthController.currentAdmin?.role.name
                                  .toUpperCase() ??
                              '',
                          style: AdminTheme.bodySmall.copyWith(
                            color: AdminTheme.textOnPrimary.withOpacity(0.8),
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(width: AdminTheme.spacingSmall),
                    IconButton(
                      onPressed: _showLogoutBottomSheet,
                      icon: const Icon(Icons.logout),
                      tooltip: 'Logout',
                    ),
                  ],
                ),
              )),
        ],
      ],
    );
  }

  Widget _buildBody(BuildContext context, bool isMobile) {
    if (isMobile) {
      return Padding(
        padding: const EdgeInsets.all(AdminTheme.spacingMedium),
        child: widget.child,
      );
    } else {
      // Desktop layout with sidebar
      return Row(
        children: [
          _buildDesktopSidebar(context),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(AdminTheme.spacingLarge),
              child: widget.child,
            ),
          ),
        ],
      );
    }
  }

  Widget _buildDesktopSidebar(BuildContext context) {
    return Container(
      width: 280,
      decoration: BoxDecoration(
        color: AdminTheme.surfaceColor,
        boxShadow: AdminTheme.cardShadow,
      ),
      child: Column(
        children: [
          // Logo Section
          Container(
            height: 120,
            padding: const EdgeInsets.all(AdminTheme.spacingLarge),
            decoration: BoxDecoration(
              gradient: AdminTheme.primaryGradient,
            ),
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: AdminTheme.textOnPrimary,
                    borderRadius:
                        BorderRadius.circular(AdminTheme.radiusMedium),
                  ),
                  child: Icon(
                    Icons.admin_panel_settings,
                    color: AdminTheme.primaryColor,
                    size: 28,
                  ),
                ),
                const SizedBox(width: AdminTheme.spacingMedium),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Money Mouthy',
                        style: AdminTheme.headingSmall.copyWith(
                          color: AdminTheme.textOnPrimary,
                        ),
                      ),
                      Text(
                        'Admin Panel',
                        style: AdminTheme.bodySmall.copyWith(
                          color: AdminTheme.textOnPrimary.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Navigation Menu
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(
                  vertical: AdminTheme.spacingMedium),
              children: [
                _buildSidebarMenuItem(
                  icon: Icons.dashboard,
                  title: 'Dashboard',
                  route: '/admin/dashboard',
                  context: context,
                ),
                _buildSidebarMenuItem(
                  icon: Icons.people,
                  title: 'User Management',
                  route: '/admin/users',
                  context: context,
                ),
                _buildSidebarMenuItem(
                  icon: Icons.timeline,
                  title: 'User Activity',
                  route: '/admin/activity',
                  context: context,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSidebarMenuItem({
    required IconData icon,
    required String title,
    required String route,
    required BuildContext context,
  }) {
    final isSelected = Get.currentRoute == route;

    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AdminTheme.spacingMedium,
        vertical: AdminTheme.spacingXSmall,
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color:
              isSelected ? AdminTheme.primaryColor : AdminTheme.textSecondary,
        ),
        title: Text(
          title,
          style: AdminTheme.bodyLarge.copyWith(
            color:
                isSelected ? AdminTheme.primaryColor : AdminTheme.textPrimary,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
        selected: isSelected,
        selectedTileColor: AdminTheme.primaryColor.withOpacity(0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
        ),
        onTap: () {
          if (!isSelected) {
            Get.offNamed(route);
          }
        },
      ),
    );
  }

  Widget _buildMobileDrawer(BuildContext context) {
    return Drawer(
      child: Container(
        color: AdminTheme.surfaceColor,
        child: Column(
          children: [
            // Logo Section
            Container(
              height: 120,
              padding: const EdgeInsets.all(AdminTheme.spacingLarge),
              decoration: BoxDecoration(
                gradient: AdminTheme.primaryGradient,
              ),
              child: Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AdminTheme.textOnPrimary,
                      borderRadius:
                          BorderRadius.circular(AdminTheme.radiusMedium),
                    ),
                    child: Icon(
                      Icons.admin_panel_settings,
                      color: AdminTheme.primaryColor,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: AdminTheme.spacingMedium),
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Money Mouthy',
                          style: AdminTheme.headingSmall.copyWith(
                            color: AdminTheme.textOnPrimary,
                          ),
                        ),
                        Text(
                          'Admin Panel',
                          style: AdminTheme.bodySmall.copyWith(
                            color: AdminTheme.textOnPrimary.withOpacity(0.8),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Admin Profile Section
            Obx(() => Container(
                  padding: const EdgeInsets.all(AdminTheme.spacingLarge),
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: AdminTheme.borderColor),
                    ),
                  ),
                  child: Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: AdminTheme.primaryColor,
                        radius: 24,
                        child: Text(
                          adminAuthController.currentAdmin?.name
                                  .substring(0, 1)
                                  .toUpperCase() ??
                              'A',
                          style: AdminTheme.bodyLarge.copyWith(
                            color: AdminTheme.textOnPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      const SizedBox(width: AdminTheme.spacingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              adminAuthController.currentAdmin?.name ?? 'Admin',
                              style: AdminTheme.bodyLarge.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Text(
                              adminAuthController.currentAdmin?.role.name
                                      .toUpperCase() ??
                                  '',
                              style: AdminTheme.bodySmall.copyWith(
                                color: AdminTheme.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                )),

            // Navigation Menu
            Expanded(
              child: ListView(
                padding: const EdgeInsets.symmetric(
                    vertical: AdminTheme.spacingMedium),
                children: [
                  _buildMobileMenuItem(
                    icon: Icons.dashboard,
                    title: 'Dashboard',
                    route: '/admin/dashboard',
                    context: context,
                  ),
                  _buildMobileMenuItem(
                    icon: Icons.people,
                    title: 'User Management',
                    route: '/admin/users',
                    context: context,
                  ),
                  _buildMobileMenuItem(
                    icon: Icons.timeline,
                    title: 'User Activity',
                    route: '/admin/activity',
                    context: context,
                  ),
                ],
              ),
            ),

            // Logout Section
            Container(
              padding: const EdgeInsets.all(AdminTheme.spacingLarge),
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: AdminTheme.borderColor),
                ),
              ),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _showLogoutBottomSheet();
                  },
                  icon: const Icon(Icons.logout),
                  label: const Text('Logout'),
                  style: AdminTheme.errorButtonStyle,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileMenuItem({
    required IconData icon,
    required String title,
    required String route,
    required BuildContext context,
  }) {
    final isSelected = Get.currentRoute == route;

    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AdminTheme.spacingMedium,
        vertical: AdminTheme.spacingXSmall,
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color:
              isSelected ? AdminTheme.primaryColor : AdminTheme.textSecondary,
        ),
        title: Text(
          title,
          style: AdminTheme.bodyLarge.copyWith(
            color:
                isSelected ? AdminTheme.primaryColor : AdminTheme.textPrimary,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
        selected: isSelected,
        selectedTileColor: AdminTheme.primaryColor.withOpacity(0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
        ),
        onTap: () {
          Navigator.of(context).pop();
          if (!isSelected) {
            Get.offNamed(route);
          }
        },
      ),
    );
  }

  Widget _buildBottomNavigation(BuildContext context) {
    return BottomNavigationBar(
      currentIndex: _selectedIndex,
      onTap: (index) {
        setState(() {
          _selectedIndex = index;
        });

        switch (index) {
          case 0:
            Get.offNamed('/admin/dashboard');
            break;
          case 1:
            Get.offNamed('/admin/users');
            break;
          case 2:
            Get.offNamed('/admin/activity');
            break;
        }
      },
      type: BottomNavigationBarType.fixed,
      backgroundColor: AdminTheme.surfaceColor,
      selectedItemColor: AdminTheme.primaryColor,
      unselectedItemColor: AdminTheme.textSecondary,
      selectedLabelStyle: AdminTheme.bodySmall.copyWith(
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: AdminTheme.bodySmall,
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.dashboard),
          label: 'Dashboard',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.people),
          label: 'Users',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.timeline),
          label: 'Activity',
        ),
      ],
    );
  }

  void _showLogoutBottomSheet() {
    ConfirmationBottomSheet.show(
      context: context,
      title: 'Confirm Logout',
      message: 'Are you sure you want to logout from the admin panel?',
      confirmText: 'Logout',
      cancelText: 'Cancel',
      confirmColor: AdminTheme.errorColor,
      icon: Icons.logout,
      onConfirm: () {
        adminAuthController.signOut();
        Get.offAllNamed('/admin/login');
      },
    );
  }
}
