#!/bin/bash

# Firebase Functions Setup Script for Money Mouthy App
# This script sets up Firebase Functions with Stripe integration

echo "🚀 Setting up Firebase Functions for Money Mouthy..."

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI not found. Please install it first:"
    echo "npm install -g firebase-tools"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ Please run this script from the Flutter project root directory"
    exit 1
fi

# Install Functions dependencies
echo "📦 Installing Firebase Functions dependencies..."
cd functions
npm install

# Check if user is logged in to Firebase
echo "🔐 Checking Firebase authentication..."
if ! firebase projects:list &> /dev/null; then
    echo "Please login to Firebase:"
    firebase login
fi

# Set up Stripe configuration
echo "⚙️ Setting up Stripe configuration..."
echo ""
echo "You need to configure your Stripe keys. Get them from:"
echo "https://dashboard.stripe.com/apikeys"
echo ""

read -p "Enter your Stripe TEST secret key (sk_test_...): " STRIPE_SECRET_KEY
read -p "Enter your Stripe TEST publishable key (pk_test_...): " STRIPE_PUBLISHABLE_KEY

# Validate keys
if [[ ! $STRIPE_SECRET_KEY =~ ^sk_test_ ]]; then
    echo "⚠️ Warning: This doesn't look like a test secret key"
fi

if [[ ! $STRIPE_PUBLISHABLE_KEY =~ ^pk_test_ ]]; then
    echo "⚠️ Warning: This doesn't look like a test publishable key"
fi

# Set Firebase Functions config
echo "🔧 Setting Firebase Functions configuration..."
firebase functions:config:set stripe.secret_key="$STRIPE_SECRET_KEY"
firebase functions:config:set stripe.publishable_key="$STRIPE_PUBLISHABLE_KEY"
firebase functions:config:set stripe.webhook_secret="temp_webhook_secret"

# Create local config for emulator
echo "📝 Creating local configuration for emulator..."
cat > .runtimeconfig.json << EOF
{
  "stripe": {
    "secret_key": "$STRIPE_SECRET_KEY",
    "publishable_key": "$STRIPE_PUBLISHABLE_KEY",
    "webhook_secret": "temp_webhook_secret"
  }
}
EOF

# Build functions
echo "🔨 Building Functions..."
npm run build

# Go back to project root
cd ..

echo ""
echo "✅ Firebase Functions setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Test locally: firebase emulators:start"
echo "2. Deploy: firebase deploy --only functions"
echo "3. Set up Stripe webhook in dashboard"
echo "4. Update webhook secret: firebase functions:config:set stripe.webhook_secret=\"whsec_...\""
echo ""
echo "🔗 Useful commands:"
echo "• Start emulators: firebase emulators:start"
echo "• Deploy functions: firebase deploy --only functions"
echo "• View logs: firebase functions:log"
echo "• View config: firebase functions:config:get"
echo ""
echo "📱 Your Flutter app is now configured to use Firebase Functions!"
echo "The payment system will use:"
echo "• Local: http://localhost:5001/money-mouthy/us-central1"
echo "• Production: https://us-central1-money-mouthy.cloudfunctions.net"
