import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'logout_service.dart';

class UserStatusService {
  static final UserStatusService _instance = UserStatusService._internal();
  factory UserStatusService() => _instance;
  UserStatusService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Check if current user is blocked
  Future<bool> isCurrentUserBlocked() async {
    final user = _auth.currentUser;
    if (user == null) return false;

    try {
      final doc = await _firestore
          .collection('users')
          .doc(user.uid)
          .get()
          .timeout(const Duration(seconds: 10));

      if (doc.exists) {
        final userData = doc.data()!;
        final status = userData['status'] ?? 'active';
        return status == 'blocked';
      }
    } catch (e) {
      debugPrint('Error checking user status: $e');
    }

    return false;
  }

  /// Handle blocked user - logout and show dialog
  Future<void> handleBlockedUser(BuildContext context) async {
    debugPrint('UserStatusService: Handling blocked user');

    // Perform logout to clear all data
    await LogoutService.performLogout();

    // Check if context is still valid before showing dialog
    if (context.mounted) {
      _showBlockedUserDialog(context);
    }
  }

  /// Show blocked user dialog with options
  void _showBlockedUserDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.block, color: Colors.red, size: 24),
              SizedBox(width: 8),
              Text('Account Blocked'),
            ],
          ),
          content: const Text(
            'Your account has been blocked. Please contact support for assistance.',
            style: TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Navigate to login screen
                if (Get.isRegistered<GetMaterialController>()) {
                  Get.offAllNamed('/login');
                } else {
                  Navigator.pushNamedAndRemoveUntil(
                    context,
                    '/login',
                    (route) => false,
                  );
                }
              },
              child: const Text('Back to Login'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Navigate to support page
                if (Get.isRegistered<GetMaterialController>()) {
                  Get.toNamed('/support');
                } else {
                  Navigator.pushNamed(context, '/support');
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0f172a),
              ),
              child: const Text(
                'Contact Support',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Check user status periodically (can be called from main navigation)
  Future<void> checkUserStatusPeriodically(BuildContext context) async {
    final user = _auth.currentUser;
    if (user == null) return;

    try {
      final isBlocked = await isCurrentUserBlocked();
      if (isBlocked && context.mounted) {
        await handleBlockedUser(context);
      }
    } catch (e) {
      debugPrint('Error in periodic user status check: $e');
    }
  }

  /// Listen to user status changes in real-time
  Stream<bool> getUserStatusStream() {
    final user = _auth.currentUser;
    if (user == null) {
      return Stream.value(false);
    }

    return _firestore
        .collection('users')
        .doc(user.uid)
        .snapshots()
        .map((snapshot) {
      if (snapshot.exists) {
        final userData = snapshot.data()!;
        final status = userData['status'] ?? 'active';
        return status == 'blocked';
      }
      return false;
    }).handleError((error) {
      debugPrint('Error in user status stream: $error');
      return false;
    });
  }
}
