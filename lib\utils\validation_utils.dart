/// Utility class for common validation functions
class ValidationUtils {
  /// Email validation regex pattern
  /// Matches most common email formats including:
  /// - Letters, numbers, dots, hyphens, underscores in local part
  /// - Domain with at least one dot
  /// - TLD with 2-4 characters
  static final RegExp _emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');

  /// Validates email format
  /// Returns true if email is valid, false otherwise
  static bool isValidEmail(String email) {
    if (email.isEmpty) return false;
    return _emailRegex.hasMatch(email.trim());
  }

  /// Validates email and returns error message if invalid
  /// Returns null if email is valid
  static String? validateEmail(String? email) {
    if (email == null || email.trim().isEmpty) {
      return 'Please enter your email';
    }
    
    if (!isValidEmail(email)) {
      return 'Please enter a valid email address';
    }
    
    return null;
  }

  /// Validates password and returns error message if invalid
  /// Returns null if password is valid
  static String? validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return 'Please enter your password';
    }
    
    if (password.length < 6) {
      return 'Password must be at least 6 characters';
    }
    
    return null;
  }

  /// Validates name and returns error message if invalid
  /// Returns null if name is valid
  static String? validateName(String? name) {
    if (name == null || name.trim().isEmpty) {
      return 'Please enter your name';
    }
    
    return null;
  }

  /// Validates password confirmation
  /// Returns null if passwords match
  static String? validatePasswordConfirmation(String? password, String? confirmPassword) {
    if (confirmPassword == null || confirmPassword.isEmpty) {
      return 'Please confirm your password';
    }
    
    if (password != confirmPassword) {
      return 'Passwords do not match';
    }
    
    return null;
  }
}
