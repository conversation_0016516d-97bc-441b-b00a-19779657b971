// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyD_lcbaRg1tNGkh0Gjo4fzphQBVqqTJ0PM',
    appId: '1:717677160958:web:09db1e2bfc9bdee1a738af',
    messagingSenderId: '717677160958',
    projectId: 'money-mouthy',
    authDomain: 'money-mouthy.firebaseapp.com',
    storageBucket: 'money-mouthy.firebasestorage.app',
    measurementId: 'G-B7M1MD0ZJW',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyC1ydzTXfkhy-dCry83jPtIqDbUrcWx2Ow',
    appId: '1:717677160958:android:f5bc9db3c362da16a738af',
    messagingSenderId: '717677160958',
    projectId: 'money-mouthy',
    storageBucket: 'money-mouthy.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyC7VzLvXOHHTKDK-wQiC40XtyvxM4x5BZ0',
    appId: '1:717677160958:ios:be9f11f341b97df8a738af',
    messagingSenderId: '717677160958',
    projectId: 'money-mouthy',
    storageBucket: 'money-mouthy.firebasestorage.app',
    iosClientId: '717677160958-5bsbq2vlrf918n816hd0irdao6gqvimo.apps.googleusercontent.com',
    iosBundleId: 'com.example.moneyMouthyTwo',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyC7VzLvXOHHTKDK-wQiC40XtyvxM4x5BZ0',
    appId: '1:717677160958:ios:be9f11f341b97df8a738af',
    messagingSenderId: '717677160958',
    projectId: 'money-mouthy',
    storageBucket: 'money-mouthy.firebasestorage.app',
    iosClientId: '717677160958-5bsbq2vlrf918n816hd0irdao6gqvimo.apps.googleusercontent.com',
    iosBundleId: 'com.example.moneyMouthyTwo',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyD_lcbaRg1tNGkh0Gjo4fzphQBVqqTJ0PM',
    appId: '1:717677160958:web:b8697cb253492500a738af',
    messagingSenderId: '717677160958',
    projectId: 'money-mouthy',
    authDomain: 'money-mouthy.firebaseapp.com',
    storageBucket: 'money-mouthy.firebasestorage.app',
    measurementId: 'G-FPP7HWFQKT',
  );

}