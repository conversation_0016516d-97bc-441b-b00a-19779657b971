rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    function isSignedIn() {
      return request.auth != null;
    }
    function isVerified() {
      // Email verification requirement removed - only check if user is signed in
      return isSignedIn();
    }

    // Each user can read/write their own document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // usernames uniqueness registry
    match /usernames/{uname} {
      // Anyone logged in can read to perform availability checks
      allow get, list: if request.auth != null;
      // Create only if document does not yet exist and the uid matches
      allow create: if request.auth != null &&
                    !exists(/databases/$(database)/documents/usernames/$(uname)) &&
                    request.resource.data.uid == request.auth.uid;
      // Allow owning user to delete (e.g., account deletion)
      allow delete: if request.auth != null && resource.data.uid == request.auth.uid;
    }

    // Admin collections - only accessible by verified admins
    match /admins/{adminId} {
      allow read, write: if request.auth != null &&
                         request.auth.uid == adminId &&
                         isVerified();
    }

    match /admin_logs/{logId} {
      allow read, write: if request.auth != null &&
                         exists(/databases/$(database)/documents/admins/$(request.auth.uid)) &&
                         isVerified();
    }

    match /user_activities/{activityId} {
      allow read, write: if request.auth != null &&
                         exists(/databases/$(database)/documents/admins/$(request.auth.uid)) &&
                         isVerified();
    }

    // Other collections (posts, wallets, etc.)
    match /{document=**} {
      allow read, write: if isVerified();
    }
  }
} 