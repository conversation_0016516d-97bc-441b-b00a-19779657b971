import 'package:get/get.dart';
import '../screens/admin_login_screen.dart';
import '../screens/admin_dashboard_screen.dart';
import '../screens/user_management_screen.dart';

import '../screens/user_activity_screen.dart';
import '../middleware/admin_auth_middleware.dart';
import '../bindings/admin_bindings.dart';

class AdminRoutes {
  static const String login = '/admin/login';
  static const String dashboard = '/admin/dashboard';
  static const String users = '/admin/users';
  static const String activity = '/admin/activity';
  static const String analytics = '/admin/analytics';
  static const String admins = '/admin/admins';
  static const String settings = '/admin/settings';
  static const String help = '/admin/help';

  static List<GetPage> getPages() {
    return [
      // Admin Login (no auth required)
      GetPage(
        name: login,
        page: () => const AdminLoginScreen(),
        binding: AdminBindings(),
      ),

      // Protected Admin Routes
      GetPage(
        name: dashboard,
        page: () => const AdminDashboardScreen(),
        middlewares: [AdminAuthMiddleware()],
        binding: AdminDashboardBindings(),
      ),

      GetPage(
        name: users,
        page: () => const UserManagementScreen(),
        middlewares: [AdminAuthMiddleware()],
        binding: UserManagementBindings(),
      ),

      GetPage(
        name: activity,
        page: () => const UserActivityScreen(),
        middlewares: [AdminAuthMiddleware()],
        binding: UserActivityBindings(),
      ),
    ];
  }
}
