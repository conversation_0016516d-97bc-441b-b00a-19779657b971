import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/user_management_model.dart';
import '../controllers/user_activity_controller.dart';
import '../controllers/wallet_management_controller.dart';
import 'user_status_chip.dart';

class UserDetailsDialog extends StatefulWidget {
  final UserManagementModel user;

  const UserDetailsDialog({
    super.key,
    required this.user,
  });

  @override
  State<UserDetailsDialog> createState() => _UserDetailsDialogState();
}

class _UserDetailsDialogState extends State<UserDetailsDialog>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _userActivityController = Get.put(UserActivityController());
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadUserData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadUserData() {
    // Load user activities and wallet data
    _userActivityController.getUserActivities(widget.user.id);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Header
        _buildHeader(),

        // Tabs
        _buildTabBar(),

        // Tab Content
        Expanded(
          child: MouseRegion(
            child: TabBarView(
              controller: _tabController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildProfileTab(),
                _buildWalletTab(),
                _buildActivityTab(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          // Profile Image
          CircleAvatar(
            radius: 30,
            backgroundImage: widget.user.profileImageUrl != null
                ? NetworkImage(widget.user.profileImageUrl!)
                : null,
            child: widget.user.profileImageUrl == null
                ? Text(
                    widget.user.name?.substring(0, 1).toUpperCase() ?? 'U',
                    style: const TextStyle(
                        fontSize: 24, fontWeight: FontWeight.bold),
                  )
                : null,
          ),
          const SizedBox(width: 16),

          // User Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.user.name ?? 'No Name',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.user.email,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
                if (widget.user.username != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    '@${widget.user.username}',
                    style: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 14,
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Status only (close button is handled by bottom sheet)
          UserStatusChip(status: widget.user.status),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: Colors.deepPurple,
        unselectedLabelColor: Colors.grey[600],
        indicatorColor: Colors.deepPurple,
        tabs: const [
          Tab(text: 'Profile'),
          Tab(text: 'Wallet'),
          Tab(text: 'Activity'),
        ],
      ),
    );
  }

  Widget _buildProfileTab() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoSection('Basic Information', [
            _buildInfoRow('User ID', widget.user.id),
            _buildInfoRow('Email', widget.user.email),
            _buildInfoRow('Name', widget.user.name ?? 'Not set'),
            _buildInfoRow('Username', widget.user.username ?? 'Not set'),
            _buildInfoRow('Bio', widget.user.bio ?? 'Not set'),
          ]),
          const SizedBox(height: 24),
          _buildInfoSection('Account Status', [
            _buildInfoRow('Status', widget.user.status.name.toUpperCase()),
            _buildInfoRow(
                'Email Verified', widget.user.emailVerified ? 'Yes' : 'No'),
            _buildInfoRow('Profile Completed',
                widget.user.profileCompleted ? 'Yes' : 'No'),
            _buildInfoRow('Created At', _formatDateTime(widget.user.createdAt)),
            _buildInfoRow(
                'Last Login',
                widget.user.lastLogin != null
                    ? _formatDateTime(widget.user.lastLogin!)
                    : 'Never'),
          ]),
        ],
      ),
    );
  }

  Widget _buildWalletTab() {
    final walletController = Get.find<WalletManagementController>();
    final wallet = walletController.getWalletByUserId(widget.user.id);

    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with actions
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Wallet',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              Row(
                children: [
                  ElevatedButton.icon(
                    onPressed: () => _showAddFundsDialog(wallet),
                    icon: const Icon(Icons.add, size: 16),
                    label: const Text('Add Funds'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: () => _showDeductFundsDialog(wallet),
                    icon: const Icon(Icons.remove, size: 16),
                    label: const Text('Deduct'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  // const SizedBox(width: 8),
                  // ElevatedButton.icon(
                  //   onPressed: () => _toggleWalletStatus(wallet),
                  //   icon: Icon(
                  //     wallet?.isBlocked == true
                  //         ? Icons.check_circle
                  //         : Icons.block,
                  //     size: 16,
                  //   ),
                  //   label:
                  //       Text(wallet?.isBlocked == true ? 'Unblock' : 'Block'),
                  //   style: ElevatedButton.styleFrom(
                  //     backgroundColor:
                  //         wallet?.isBlocked == true ? Colors.green : Colors.red,
                  //     foregroundColor: Colors.white,
                  //   ),
                  // ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Wallet stats cards
          Row(
            children: [
              Expanded(
                child: _buildWalletStatCard(
                  'Balance',
                  '\$${wallet?.balance.toStringAsFixed(2) ?? '0.00'}',
                  Icons.account_balance_wallet,
                  Colors.green,
                ),
              ),
              // const SizedBox(width: 16),
              // Expanded(
              //   child: _buildWalletStatCard(
              //     'Total Earned',
              //     '\$${wallet?.totalEarnings.toStringAsFixed(2) ?? '0.00'}',
              //     Icons.trending_up,
              //     Colors.blue,
              //   ),
              // ),
              // const SizedBox(width: 16),
              // Expanded(
              //   child: _buildWalletStatCard(
              //     'Total Spent',
              //     '\$${wallet?.totalSpent.toStringAsFixed(2) ?? '0.00'}',
              //     Icons.trending_down,
              //     Colors.red,
              //   ),
              // ),
            ],
          ),
          const SizedBox(height: 24),

          // Recent transactions
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Transactions',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              TextButton.icon(
                onPressed: () => _showAllTransactions(wallet),
                icon: const Icon(Icons.list, size: 16),
                label: const Text('View All'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: _buildTransactionsList(wallet),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityTab() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'User Activity',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[200]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Text('Loading activities...'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[200]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(children: children),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Widget _buildWalletStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _showAddFundsDialog(wallet) {
    final amountController = TextEditingController();
    final reasonController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('Add Funds'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: amountController,
              decoration: const InputDecoration(
                labelText: 'Amount (\$)',
                prefixIcon: Icon(Icons.attach_money),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason',
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final amount = double.tryParse(amountController.text);
              if (amount != null &&
                  amount > 0 &&
                  reasonController.text.isNotEmpty) {
                Get.back();
                final walletController = Get.find<WalletManagementController>();
                final success = await walletController.adjustWalletBalance(
                  widget.user.id,
                  amount,
                  reasonController.text,
                );
                if (success) {
                  Get.snackbar('Success', 'Funds added successfully');
                }
              }
            },
            child: const Text('Add Funds'),
          ),
        ],
      ),
    );
  }

  void _showDeductFundsDialog(wallet) {
    final amountController = TextEditingController();
    final reasonController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('Deduct Funds'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: amountController,
              decoration: const InputDecoration(
                labelText: 'Amount (\$)',
                prefixIcon: Icon(Icons.attach_money),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason',
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final amount = double.tryParse(amountController.text);
              if (amount != null &&
                  amount > 0 &&
                  reasonController.text.isNotEmpty) {
                Get.back();
                final walletController = Get.find<WalletManagementController>();
                final success = await walletController.adjustWalletBalance(
                  widget.user.id,
                  -amount, // Negative for deduction
                  reasonController.text,
                );
                if (success) {
                  Get.snackbar('Success', 'Funds deducted successfully');
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('Deduct Funds'),
          ),
        ],
      ),
    );
  }

  void _toggleWalletStatus(wallet) async {
    final walletController = Get.find<WalletManagementController>();
    final isBlocked = wallet?.isBlocked == true;

    final success = await walletController.toggleWalletStatus(
      widget.user.id,
      isBlocked ? 'Wallet unblocked by admin' : 'Wallet blocked by admin',
    );

    if (success) {
      Get.snackbar(
          'Success',
          isBlocked
              ? 'Wallet unblocked successfully'
              : 'Wallet blocked successfully');
    }
  }

  void _showAllTransactions(wallet) {
    Get.dialog(
      Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'All Transactions - ${widget.user.name ?? widget.user.email}',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: FutureBuilder(
                  future: Get.find<WalletManagementController>()
                      .getWalletTransactions(widget.user.id, limit: 100),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    if (snapshot.hasError) {
                      return Center(
                        child: Text('Error: ${snapshot.error}'),
                      );
                    }

                    final transactions = snapshot.data ?? [];

                    if (transactions.isEmpty) {
                      return const Center(
                        child: Text('No transactions found'),
                      );
                    }

                    return ListView.builder(
                      itemCount: transactions.length,
                      itemBuilder: (context, index) {
                        return _buildTransactionItem(transactions[index]);
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTransactionsList(wallet) {
    if (wallet == null) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[200]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Text('No wallet found'),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[200]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: FutureBuilder(
        future: Get.find<WalletManagementController>()
            .getWalletTransactions(widget.user.id, limit: 10),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (snapshot.hasError) {
            return Center(
              child: Text(
                'Error loading transactions: ${snapshot.error}',
                style: const TextStyle(color: Colors.red),
              ),
            );
          }

          final transactions = snapshot.data ?? [];

          if (transactions.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.receipt_long,
                    size: 48,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'No transactions found',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(8),
            itemCount: transactions.length,
            itemBuilder: (context, index) {
              final transaction = transactions[index];
              return _buildTransactionItem(transaction);
            },
          );
        },
      ),
    );
  }

  Widget _buildTransactionItem(transaction) {
    final isCredit = transaction.type.toString().contains('credit');
    final amount = transaction.amount ?? 0.0;
    final description = transaction.description ?? 'No description';
    final timestamp = transaction.timestamp;
    final isAdminAction = transaction.isAdminAction ?? false;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          // Transaction Icon
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isCredit
                  ? Colors.green.withOpacity(0.1)
                  : Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              isCredit ? Icons.add : Icons.remove,
              color: isCredit ? Colors.green : Colors.red,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),

          // Transaction Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        description,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Text(
                      '${isCredit ? '+' : '-'}\$${amount.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: isCredit ? Colors.green : Colors.red,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      timestamp != null
                          ? _formatDateTime(timestamp)
                          : 'Unknown date',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                    if (isAdminAction)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.blue.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'Admin',
                          style: TextStyle(
                            color: Colors.blue[700],
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
