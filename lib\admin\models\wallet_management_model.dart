import 'package:cloud_firestore/cloud_firestore.dart';
import '../../models/transaction_model.dart';

enum WalletStatus {
  active,
  blocked,
}

class WalletManagementModel {
  final String userId;
  final double balance;
  final WalletStatus status;
  final DateTime createdAt;
  final DateTime lastUpdated;
  final double totalEarnings;
  final double totalSpent;
  final int transactionCount;
  final Map<String, dynamic>? metadata;

  const WalletManagementModel({
    required this.userId,
    required this.balance,
    required this.status,
    required this.createdAt,
    required this.lastUpdated,
    required this.totalEarnings,
    required this.totalSpent,
    required this.transactionCount,
    this.metadata,
  });

  Map<String, dynamic> toMap() {
    return {
      'balance': balance,
      'status': status.name,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastUpdated': lastUpdated.millisecondsSinceEpoch,
      'totalEarnings': totalEarnings,
      'totalSpent': totalSpent,
      'transactionCount': transactionCount,
      'metadata': metadata,
    };
  }

  factory WalletManagementModel.fromMap(
      String userId, Map<String, dynamic> map) {
    DateTime createdAt;
    final createdAtValue = map['createdAt'];
    if (createdAtValue is int) {
      createdAt = DateTime.fromMillisecondsSinceEpoch(createdAtValue);
    } else if (createdAtValue is Timestamp) {
      createdAt = createdAtValue.toDate();
    } else {
      createdAt = DateTime.now();
    }

    DateTime lastUpdated;
    final lastUpdatedValue = map['lastUpdated'];
    if (lastUpdatedValue is int) {
      lastUpdated = DateTime.fromMillisecondsSinceEpoch(lastUpdatedValue);
    } else if (lastUpdatedValue is Timestamp) {
      lastUpdated = lastUpdatedValue.toDate();
    } else {
      lastUpdated = DateTime.now();
    }

    return WalletManagementModel(
      userId: userId,
      balance: (map['balance'] ?? 0.0).toDouble(),
      status: WalletStatus.values.firstWhere(
        (status) => status.name == map['status'],
        orElse: () => WalletStatus.active,
      ),
      createdAt: createdAt,
      lastUpdated: lastUpdated,
      totalEarnings: (map['totalEarnings'] ?? 0.0).toDouble(),
      totalSpent: (map['totalSpent'] ?? 0.0).toDouble(),
      transactionCount: map['transactionCount'] ?? 0,
      metadata: map['metadata'],
    );
  }

  WalletManagementModel copyWith({
    double? balance,
    WalletStatus? status,
    DateTime? lastUpdated,
    double? totalEarnings,
    double? totalSpent,
    int? transactionCount,
    Map<String, dynamic>? metadata,
  }) {
    return WalletManagementModel(
      userId: userId,
      balance: balance ?? this.balance,
      status: status ?? this.status,
      createdAt: createdAt,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      totalEarnings: totalEarnings ?? this.totalEarnings,
      totalSpent: totalSpent ?? this.totalSpent,
      transactionCount: transactionCount ?? this.transactionCount,
      metadata: metadata ?? this.metadata,
    );
  }

  bool get isBlocked => status == WalletStatus.blocked;
  bool get isActive => status == WalletStatus.active;
}

class AdminTransactionModel extends TransactionModel {
  final String userId;
  final String? adminId;
  final String? adminNote;
  final bool isAdminAction;

  const AdminTransactionModel({
    required super.id,
    required super.type,
    required super.amount,
    required super.description,
    required super.timestamp,
    required super.status,
    required this.userId,
    super.postId,
    super.paymentMethodId,
    super.externalTransactionId,
    super.metadata,
    this.adminId,
    this.adminNote,
    this.isAdminAction = false,
  });

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    map.addAll({
      'userId': userId,
      'adminId': adminId,
      'adminNote': adminNote,
      'isAdminAction': isAdminAction,
    });
    return map;
  }

  factory AdminTransactionModel.fromMap(String id, Map<String, dynamic> map) {
    final baseTransaction = TransactionModel.fromMap(id, map);

    return AdminTransactionModel(
      id: baseTransaction.id,
      type: baseTransaction.type,
      amount: baseTransaction.amount,
      description: baseTransaction.description,
      timestamp: baseTransaction.timestamp,
      status: baseTransaction.status,
      userId: map['userId'] ?? '',
      postId: baseTransaction.postId,
      paymentMethodId: baseTransaction.paymentMethodId,
      externalTransactionId: baseTransaction.externalTransactionId,
      metadata: baseTransaction.metadata,
      adminId: map['adminId'],
      adminNote: map['adminNote'],
      isAdminAction: map['isAdminAction'] ?? false,
    );
  }

  @override
  AdminTransactionModel copyWith({
    String? id,
    TransactionType? type,
    double? amount,
    String? description,
    DateTime? timestamp,
    TransactionStatus? status,
    String? postId,
    String? paymentMethodId,
    String? externalTransactionId,
    Map<String, dynamic>? metadata,
    String? userId,
    String? adminId,
    String? adminNote,
    bool? isAdminAction,
  }) {
    return AdminTransactionModel(
      id: id ?? this.id,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
      userId: userId ?? this.userId,
      postId: postId ?? this.postId,
      paymentMethodId: paymentMethodId ?? this.paymentMethodId,
      externalTransactionId:
          externalTransactionId ?? this.externalTransactionId,
      metadata: metadata ?? this.metadata,
      adminId: adminId ?? this.adminId,
      adminNote: adminNote ?? this.adminNote,
      isAdminAction: isAdminAction ?? this.isAdminAction,
    );
  }
}
