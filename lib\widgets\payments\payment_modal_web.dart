// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_stripe_web/flutter_stripe_web.dart';
// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:http/http.dart' as http;
// import 'dart:convert';
// import 'package:web/web.dart' as web;

// class PaymentModalWeb extends StatefulWidget {
//   final double amount;
//   final String clientSecret;
//   final VoidCallback? onSuccess;
//   final VoidCallback? onCancel;

//   const PaymentModalWeb({
//     super.key,
//     required this.amount,
//     required this.clientSecret,
//     this.onSuccess,
//     this.onCancel,
//   });

//   @override
//   State<PaymentModalWeb> createState() => _PaymentModalWebState();
// }

// class _PaymentModalWebState extends State<PaymentModalWeb> {
//   // String? _clientSecret;
//   bool _isLoading = false;
//   bool _isProcessing = false;
//   String? _errorMessage;

//   @override
//   void initState() {
//     super.initState();
//     // _createPaymentIntent();
//   }

//   // Future<void> _createPaymentIntent() async {
//   //   setState(() {
//   //     _isLoading = true;
//   //     _errorMessage = null;
//   //   });
//   //
//   //   try {
//   //     final user = FirebaseAuth.instance.currentUser;
//   //     if (user?.email == null) {
//   //       throw Exception('User not authenticated');
//   //     }
//   //
//   //     final response = await http.post(
//   //       Uri.parse(
//   //           'https://us-central1-money-mouthy.cloudfunctions.net/createPaymentIntent'),
//   //       headers: {'Content-Type': 'application/json'},
//   //       body: json.encode({
//   //         'amount': (widget.amount * 100).round(), // Convert to cents
//   //         'currency': 'usd',
//   //         'email': user!.email,
//   //       }),
//   //     );

//   //     if (response.statusCode == 200) {
//   //       final data = json.decode(response.body);
//   //       if (data['success'] == true) {
//   //         setState(() {
//   //           _clientSecret = data['paymentIntent'];
//   //           _isLoading = false;
//   //         });
//   //       } else {
//   //         throw Exception(data['error'] ?? 'Failed to create payment intent');
//   //       }
//   //     } else {
//   //       throw Exception('HTTP ${response.statusCode}: ${response.body}');
//   //     }
//   //   } catch (e) {
//   //     setState(() {
//   //       _errorMessage = e.toString();
//   //       _isLoading = false;
//   //     });
//   //   }
//   // }

//   Future<void> _processPayment() async {
//     if (widget.clientSecret == null) return;

//     setState(() {
//       _isProcessing = true;
//       _errorMessage = null;
//     });

//     try {
//       // Get current URL for return URL
//       final currentUrl = web.window.location.href;
//       final baseUrl =
//           currentUrl.split('?')[0]; // Remove any existing query params

//       final result = await WebStripe.instance.confirmPaymentElement(
//         ConfirmPaymentElementOptions(
//           confirmParams: ConfirmPaymentParams(
//             return_url: '$baseUrl?payment_modal=true',
//           ),
//         ),
//       );

//       debugPrint('Payment result: $result');

//       // Check if payment was successful without redirect
//       if (result.status == PaymentIntentsStatus.Succeeded) {
//         await _handlePaymentSuccess(result.id!);
//       } else if (result.status == PaymentIntentsStatus.Processing) {
//         // Payment is processing, we'll handle this via webhook or redirect
//         _showProcessingMessage();
//       } else {
//         throw Exception('Payment failed with status: ${result.status}');
//       }
//     } catch (e) {
//       debugPrint('Payment error: $e');

//       // Check if this is a redirect scenario (most common for web payments)
//       if (e.toString().contains('redirect') ||
//           e.toString().contains('authentication')) {
//         // Payment will redirect, so we close the modal
//         // The redirect handler will take care of the rest
//         if (mounted) {
//           Navigator.of(context).pop();
//         }
//       } else {
//         setState(() {
//           _errorMessage = _extractErrorMessage(e);
//           _isProcessing = false;
//         });
//       }
//     }
//   }

//   /// Extract user-friendly error message from various error types
//   String _extractErrorMessage(dynamic error) {
//     final errorString = error.toString();

//     // Check if it's a StripeError format
//     if (errorString.contains('StripeError(')) {
//       // Extract message from StripeError format
//       final messageMatch =
//           RegExp(r'message:\s*([^,}]+)').firstMatch(errorString);
//       if (messageMatch != null) {
//         String message = messageMatch.group(1)?.trim() ?? '';
//         // Remove quotes if present
//         if (message.startsWith('"') && message.endsWith('"')) {
//           message = message.substring(1, message.length - 1);
//         }
//         return message.isNotEmpty
//             ? message
//             : 'Payment failed. Please try again.';
//       }
//     }

//     // Check for common error patterns and provide user-friendly messages
//     if (errorString.toLowerCase().contains('declined') ||
//         errorString.toLowerCase().contains('card_error')) {
//       return 'Your payment method was declined. Please try a different card.';
//     }

//     if (errorString.toLowerCase().contains('insufficient_funds')) {
//       return 'Insufficient funds. Please check your account balance.';
//     }

//     if (errorString.toLowerCase().contains('expired_card')) {
//       return 'Your card has expired. Please use a different card.';
//     }

//     if (errorString.toLowerCase().contains('incorrect_cvc')) {
//       return 'Incorrect security code. Please check your card details.';
//     }

//     if (errorString.toLowerCase().contains('processing_error')) {
//       return 'Payment processing error. Please try again.';
//     }

//     // Default fallback message
//     return 'Payment failed. Please try again.';
//   }

//   Future<void> _handlePaymentSuccess(String paymentIntentId) async {
//     try {
//       final user = FirebaseAuth.instance.currentUser;
//       if (user == null) {
//         throw Exception('User not authenticated');
//       }

//       // Call our Firebase Function to handle payment completion
//       final response = await http.post(
//         Uri.parse(
//             'https://us-central1-money-mouthy.cloudfunctions.net/handlePaymentCompletion'),
//         headers: {'Content-Type': 'application/json'},
//         body: json.encode({
//           'paymentIntentId': paymentIntentId,
//           'userId': user.uid,
//         }),
//       );

//       if (response.statusCode == 200) {
//         final data = json.decode(response.body);
//         if (data['success'] == true) {
//           if (mounted) {
//             Navigator.of(context).pop();
//             widget.onSuccess?.call();

//             ScaffoldMessenger.of(context).showSnackBar(
//               SnackBar(
//                 content: Text(
//                   'Payment successful! \$${widget.amount.toStringAsFixed(2)} added to your wallet.',
//                 ),
//                 backgroundColor: Colors.green,
//               ),
//             );
//           }
//         } else {
//           throw Exception(
//               data['error'] ?? 'Failed to process payment completion');
//         }
//       } else {
//         throw Exception('HTTP ${response.statusCode}: ${response.body}');
//       }
//     } catch (e) {
//       debugPrint('Payment completion error: $e');
//       setState(() {
//         _errorMessage = 'Failed to complete payment processing';
//         _isProcessing = false;
//       });
//     }
//   }

//   void _showProcessingMessage() {
//     if (mounted) {
//       Navigator.of(context).pop();

//       ScaffoldMessenger.of(context).showSnackBar(
//         const SnackBar(
//           content: Text(
//             'Payment is being processed. We\'ll update your wallet once completed.',
//           ),
//           backgroundColor: Colors.orange,
//         ),
//       );
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Dialog(
//       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
//       child: SingleChildScrollView(
//         padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 24),
//         child: Container(
//           constraints: BoxConstraints(
//               maxWidth: kIsWeb && MediaQuery.of(context).size.width > 800
//                   ? 800
//                   : double.infinity),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.stretch,
//             children: [
//               // Header
//               Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   Text(
//                     'Add Funds',
//                     style: Theme.of(context).textTheme.headlineSmall?.copyWith(
//                           fontWeight: FontWeight.bold,
//                         ),
//                   ),
//                   IconButton(
//                     onPressed: _isProcessing
//                         ? null
//                         : () {
//                             Navigator.of(context).pop();
//                             widget.onCancel?.call();
//                           },
//                     icon: const Icon(Icons.close),
//                   ),
//                 ],
//               ),
//               const SizedBox(height: 16),

//               // Amount display
//               Container(
//                 padding: const EdgeInsets.all(16),
//                 decoration: BoxDecoration(
//                   color: Theme.of(context).colorScheme.surfaceContainerHighest,
//                   borderRadius: BorderRadius.circular(12),
//                 ),
//                 child: Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     const Text('Amount:'),
//                     Text(
//                       '\$${widget.amount.toStringAsFixed(2)}',
//                       style: Theme.of(context).textTheme.titleLarge?.copyWith(
//                             fontWeight: FontWeight.bold,
//                           ),
//                     ),
//                   ],
//                 ),
//               ),
//               const SizedBox(height: 24),

//               // Payment form or loading
//               if (_isLoading)
//                 const Center(
//                   child: Padding(
//                     padding: EdgeInsets.all(32),
//                     child: CircularProgressIndicator(),
//                   ),
//                 )
//               else if (_errorMessage != null)
//                 Column(
//                   children: [
//                     Container(
//                       padding: const EdgeInsets.all(16),
//                       decoration: BoxDecoration(
//                         color: Colors.red.shade50,
//                         borderRadius: BorderRadius.circular(12),
//                         border: Border.all(color: Colors.red.shade200),
//                       ),
//                       child: Row(
//                         children: [
//                           Icon(Icons.error, color: Colors.red.shade700),
//                           const SizedBox(width: 12),
//                           Expanded(
//                             child: Text(
//                               _errorMessage!,
//                               style: TextStyle(color: Colors.red.shade700),
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                     const SizedBox(height: 16),
//                     ElevatedButton(
//                       onPressed: () {
//                         setState(() {
//                           _errorMessage = null;
//                         });
//                         // Close the modal
//                         Navigator.of(context).pop();
//                       },
//                       child: const Text('Retry'),
//                     ),
//                   ],
//                 )
//               else
//                 Column(
//                   children: [
//                     // Payment Element
//                     Container(
//                       decoration: BoxDecoration(
//                         border: Border.all(color: Colors.grey.shade300),
//                         borderRadius: BorderRadius.circular(12),
//                       ),
//                       child: PaymentElement(
//                         autofocus: true,
//                         enablePostalCode: true,
//                         onCardChanged: (_) {},
//                         clientSecret: widget.clientSecret,
//                       ),
//                     ),
//                     const SizedBox(height: 24),

//                     // Pay button
//                     SizedBox(
//                       width: double.infinity,
//                       height: 48,
//                       child: ElevatedButton(
//                         onPressed: _isProcessing ? null : _processPayment,
//                         style: ElevatedButton.styleFrom(
//                           backgroundColor: Theme.of(context).primaryColor,
//                           foregroundColor: Colors.white,
//                           shape: RoundedRectangleBorder(
//                             borderRadius: BorderRadius.circular(12),
//                           ),
//                         ),
//                         child: _isProcessing
//                             ? const SizedBox(
//                                 height: 20,
//                                 width: 20,
//                                 child: CircularProgressIndicator(
//                                   strokeWidth: 2,
//                                   valueColor: AlwaysStoppedAnimation<Color>(
//                                       Colors.white),
//                                 ),
//                               )
//                             : Text(
//                                 'Pay \$${widget.amount.toStringAsFixed(2)}',
//                                 style: const TextStyle(
//                                   fontSize: 16,
//                                   fontWeight: FontWeight.bold,
//                                 ),
//                               ),
//                       ),
//                     ),
//                   ],
//                 ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
