import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/wallet_management_model.dart';
import '../../models/transaction_model.dart';
import 'admin_auth_controller.dart';

class WalletManagementController extends GetxController {
  static WalletManagementController get to => Get.find();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Reactive variables
  final _wallets = <WalletManagementModel>[].obs;
  final _filteredWallets = <WalletManagementModel>[].obs;
  final _transactions = <AdminTransactionModel>[].obs;
  final _userDetails = <String, Map<String, dynamic>>{}.obs;
  final _allUsers = <String>[].obs; // Track all user IDs
  final _isLoading = false.obs;
  final _errorMessage = RxnString();
  final _searchQuery = ''.obs;
  final _selectedStatus = Rxn<WalletStatus>();
  final _totalWallets = 0.obs;
  final _totalBalance = 0.0.obs;
  final _blockedWallets = 0.obs;
  final _totalStripePayments = 0.0.obs;

  StreamSubscription<QuerySnapshot>? _walletsSubscription;
  StreamSubscription<QuerySnapshot>? _transactionsSubscription;

  // Getters
  List<WalletManagementModel> get wallets => _filteredWallets;
  List<AdminTransactionModel> get transactions => _transactions;
  Map<String, Map<String, dynamic>> get userDetails => _userDetails;
  bool get isLoading => _isLoading.value;
  String? get errorMessage => _errorMessage.value;
  String get searchQuery => _searchQuery.value;
  WalletStatus? get selectedStatus => _selectedStatus.value;
  int get totalWallets => _totalWallets.value;
  double get totalBalance => _totalBalance.value;
  int get blockedWallets => _blockedWallets.value;
  double get totalStripePayments => _totalStripePayments.value;

  @override
  void onInit() {
    super.onInit();
    _initializeUsersAndWalletsStream();
    _initializeTransactionsStream();
    _setupSearchListener();
  }

  @override
  void onClose() {
    _walletsSubscription?.cancel();
    _transactionsSubscription?.cancel();
    super.onClose();
  }

  void _initializeUsersAndWalletsStream() {
    _isLoading.value = true;

    // First load all users
    _firestore.collection('users').snapshots().listen(
      (usersSnapshot) {
        _loadAllUsersData(usersSnapshot);

        // Then load wallets
        _walletsSubscription =
            _firestore.collection('wallets').snapshots().listen(
          (walletsSnapshot) {
            _processWalletsSnapshot(walletsSnapshot);
            _isLoading.value = false;
          },
          onError: (error) {
            debugPrint('Error listening to wallets: $error');
            _errorMessage.value = 'Failed to load wallets';
            _isLoading.value = false;
          },
        );
      },
      onError: (error) {
        debugPrint('Error listening to users: $error');
        _errorMessage.value = 'Failed to load users';
        _isLoading.value = false;
      },
    );
  }

  void _initializeTransactionsStream() {
    // Listen to all transactions across all wallets
    _transactionsSubscription = _firestore
        .collectionGroup('transactions')
        .orderBy('timestamp', descending: true)
        .limit(100) // Limit to recent transactions
        .snapshots()
        .listen(
      (snapshot) {
        _processTransactionsSnapshot(snapshot);
      },
      onError: (error) {
        debugPrint('Error listening to transactions: $error');
      },
    );
  }

  void _loadAllUsersData(QuerySnapshot usersSnapshot) {
    // Clear existing data
    _userDetails.clear();
    _allUsers.clear();

    // Load user details for all users
    for (final doc in usersSnapshot.docs) {
      _userDetails[doc.id] = doc.data() as Map<String, dynamic>;
      _allUsers.add(doc.id);
    }
  }

  void _processWalletsSnapshot(QuerySnapshot snapshot) {
    final walletsList = snapshot.docs.map((doc) {
      return WalletManagementModel.fromMap(
          doc.id, doc.data() as Map<String, dynamic>);
    }).toList();

    // Create actual wallets in Firestore for users without wallets
    final existingWalletUserIds = walletsList.map((w) => w.userId).toSet();
    final usersWithoutWallets = _allUsers
        .where((userId) => !existingWalletUserIds.contains(userId))
        .toList();

    if (usersWithoutWallets.isNotEmpty) {
      _createMissingWallets(usersWithoutWallets);
    }

    _wallets.value = walletsList;
    _updateStatistics();
    _applyFilters();
  }

  Future<void> _createMissingWallets(List<String> userIds) async {
    try {
      final batch = _firestore.batch();

      for (final userId in userIds) {
        final walletRef = _firestore.collection('wallets').doc(userId);
        batch.set(walletRef, {
          'balance': 2.0, // Welcome bonus
          'createdAt': FieldValue.serverTimestamp(),
          'lastUpdated': FieldValue.serverTimestamp(),
          'totalEarnings': 2.0,
          'totalSpent': 0.0,
          'transactionCount': 1,
          'status': 'active',
        });

        // Add welcome transaction
        final transactionRef = walletRef.collection('transactions').doc();
        batch.set(transactionRef, {
          'type': 'credit',
          'amount': 2.0,
          'description': 'Welcome bonus',
          'timestamp': FieldValue.serverTimestamp(),
          'status': 'completed',
        });
      }

      await batch.commit();
      debugPrint('Created ${userIds.length} missing wallets');
    } catch (e) {
      debugPrint('Error creating missing wallets: $e');
    }
  }

  void _processTransactionsSnapshot(QuerySnapshot snapshot) {
    final transactionsList = snapshot.docs.map((doc) {
      final data = doc.data() as Map<String, dynamic>;
      // Extract userId from the document path
      final userId = doc.reference.parent.parent?.id ?? '';
      data['userId'] = userId;

      return AdminTransactionModel.fromMap(doc.id, data);
    }).toList();

    _transactions.value = transactionsList;
  }

  void _updateStatistics() {
    _totalWallets.value = _wallets.length;
    _totalBalance.value =
        _wallets.fold(0.0, (total, wallet) => total + wallet.balance);
    _blockedWallets.value = _wallets.where((wallet) => wallet.isBlocked).length;

    // Calculate total Stripe payments from July 29, 2025 onwards
    final cutoffDate = DateTime(2025, 7, 29);
    _totalStripePayments.value = _transactions
        .where((transaction) =>
            transaction.type == TransactionType.credit &&
            transaction.paymentMethodId != null &&
            transaction.paymentMethodId!.isNotEmpty &&
            transaction.timestamp.isAfter(cutoffDate))
        .fold(0.0, (total, transaction) => total + transaction.amount);
  }

  void _setupSearchListener() {
    debounce(_searchQuery, (_) => _applyFilters(),
        time: const Duration(milliseconds: 300));
    ever(_selectedStatus, (_) => _applyFilters());
  }

  void _applyFilters() {
    var filtered = _wallets.toList();

    // Apply search filter (by user ID, name, username, or email)
    if (_searchQuery.value.isNotEmpty) {
      final query = _searchQuery.value.toLowerCase();
      filtered = filtered.where((wallet) {
        final userDetail = _userDetails[wallet.userId];
        return wallet.userId.toLowerCase().contains(query) ||
            (userDetail?['name']?.toString().toLowerCase().contains(query) ??
                false) ||
            (userDetail?['username']
                    ?.toString()
                    .toLowerCase()
                    .contains(query) ??
                false) ||
            (userDetail?['email']?.toString().toLowerCase().contains(query) ??
                false);
      }).toList();
    }

    // Apply status filter
    if (_selectedStatus.value != null) {
      filtered = filtered
          .where((wallet) => wallet.status == _selectedStatus.value)
          .toList();
    }

    // Sort alphabetically by user display name
    filtered.sort((a, b) {
      final displayNameA = getUserDisplayName(a.userId).toLowerCase();
      final displayNameB = getUserDisplayName(b.userId).toLowerCase();

      // If both have proper display names (not just user IDs), sort by them
      if (!displayNameA.startsWith('user-') &&
          !displayNameB.startsWith('user-')) {
        return displayNameA.compareTo(displayNameB);
      }

      // If one has a proper display name and the other doesn't, prioritize the one with a name
      if (!displayNameA.startsWith('user-') &&
          displayNameB.startsWith('user-')) {
        return -1;
      }
      if (displayNameA.startsWith('user-') &&
          !displayNameB.startsWith('user-')) {
        return 1;
      }

      // If both are just user IDs, sort by user ID
      return a.userId.compareTo(b.userId);
    });

    _filteredWallets.value = filtered;
  }

  void setSearchQuery(String query) {
    _searchQuery.value = query;
  }

  void setStatusFilter(WalletStatus? status) {
    _selectedStatus.value = status;
  }

  void clearFilters() {
    _searchQuery.value = '';
    _selectedStatus.value = null;
  }

  Future<bool> updateWalletStatus(String userId, WalletStatus newStatus,
      {String? reason}) async {
    try {
      final updateData = {
        'status': newStatus.name,
        'lastUpdated': FieldValue.serverTimestamp(),
      };

      if (reason != null) {
        updateData['statusReason'] = reason;
        updateData['statusChangedAt'] = FieldValue.serverTimestamp();
      }

      await _firestore.collection('wallets').doc(userId).update(updateData);

      // Log admin action
      await _logAdminAction(userId,
          'Wallet status changed to ${newStatus.name}${reason != null ? ': $reason' : ''}');

      return true;
    } catch (e) {
      debugPrint('Error updating wallet status: $e');
      _errorMessage.value = 'Failed to update wallet status';
      return false;
    }
  }

  Future<bool> blockWallet(String userId, String reason) async {
    return updateWalletStatus(userId, WalletStatus.blocked, reason: reason);
  }

  Future<bool> unblockWallet(String userId) async {
    try {
      await _firestore.collection('wallets').doc(userId).update({
        'status': WalletStatus.active.name,
        'statusReason': FieldValue.delete(),
        'statusChangedAt': FieldValue.delete(),
        'lastUpdated': FieldValue.serverTimestamp(),
      });

      await _logAdminAction(userId, 'Wallet unblocked');
      return true;
    } catch (e) {
      debugPrint('Error unblocking wallet: $e');
      _errorMessage.value = 'Failed to unblock wallet';
      return false;
    }
  }

  Future<bool> adjustWalletBalance(
      String userId, double amount, String reason) async {
    try {
      final walletRef = _firestore.collection('wallets').doc(userId);
      final transactionsRef = walletRef.collection('transactions');

      await _firestore.runTransaction((transaction) async {
        final walletDoc = await transaction.get(walletRef);

        if (!walletDoc.exists) {
          throw Exception('Wallet not found');
        }

        final currentBalance = (walletDoc.data()!['balance'] ?? 0.0).toDouble();
        final newBalance = currentBalance + amount;

        if (newBalance < 0) {
          throw Exception('Insufficient balance for adjustment');
        }

        // Update wallet balance
        transaction.update(walletRef, {
          'balance': newBalance,
          'lastUpdated': FieldValue.serverTimestamp(),
        });

        // Create admin transaction record
        final transactionDoc = transactionsRef.doc();
        transaction.set(transactionDoc, {
          'type': amount > 0
              ? TransactionType.credit.name
              : TransactionType.debit.name,
          'amount': amount.abs(),
          'description': 'Admin adjustment: $reason',
          'timestamp': FieldValue.serverTimestamp(),
          'status': TransactionStatus.completed.name,
          'userId': userId,
          'adminId': Get.find<AdminAuthController>().currentAdmin?.id,
          'adminNote': reason,
          'isAdminAction': true,
          'metadata': {
            'type': 'admin_adjustment',
            'previousBalance': currentBalance,
            'newBalance': newBalance,
          },
        });
      });

      await _logAdminAction(userId,
          'Wallet balance adjusted by \$${amount.toStringAsFixed(2)}: $reason');
      return true;
    } catch (e) {
      debugPrint('Error adjusting wallet balance: $e');
      _errorMessage.value = 'Failed to adjust wallet balance: ${e.toString()}';
      return false;
    }
  }

  Future<List<AdminTransactionModel>> getWalletTransactions(String userId,
      {int limit = 50}) async {
    try {
      final snapshot = await _firestore
          .collection('wallets')
          .doc(userId)
          .collection('transactions')
          .orderBy('timestamp', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        data['userId'] = userId;
        return AdminTransactionModel.fromMap(doc.id, data);
      }).toList();
    } catch (e) {
      debugPrint('Error getting wallet transactions: $e');
      return [];
    }
  }

  Future<void> _logAdminAction(String userId, String action) async {
    try {
      await _firestore.collection('admin_logs').add({
        'userId': userId,
        'action': action,
        'timestamp': FieldValue.serverTimestamp(),
        'adminId': Get.find<AdminAuthController>().currentAdmin?.id,
        'type': 'wallet_management',
      });
    } catch (e) {
      debugPrint('Error logging admin action: $e');
    }
  }

  WalletManagementModel? getWalletByUserId(String userId) {
    try {
      return _wallets.firstWhere((wallet) => wallet.userId == userId);
    } catch (e) {
      return null;
    }
  }

  Map<String, dynamic>? getUserDetails(String userId) {
    return _userDetails[userId];
  }

  String getUserDisplayName(String userId) {
    final userDetail = _userDetails[userId];
    if (userDetail != null) {
      final name = userDetail['name']?.toString();
      final username = userDetail['username']?.toString();
      final email = userDetail['email']?.toString();

      if (name != null && name.isNotEmpty) {
        return name;
      } else if (username != null && username.isNotEmpty) {
        return '@$username';
      } else if (email != null && email.isNotEmpty) {
        return email;
      }
    }
    return userId;
  }

  void clearError() {
    _errorMessage.value = null;
  }

  /// Verify and fix data consistency between users and wallets
  Future<Map<String, dynamic>> verifyDataConsistency() async {
    try {
      final results = <String, dynamic>{
        'usersWithoutWallets': <String>[],
        'walletsWithoutUsers': <String>[],
        'inconsistentData': <String>[],
        'fixedIssues': 0,
      };

      // Get all users
      final usersSnapshot = await _firestore.collection('users').get();
      final userIds = usersSnapshot.docs.map((doc) => doc.id).toSet();

      // Get all wallets
      final walletsSnapshot = await _firestore.collection('wallets').get();
      final walletUserIds = walletsSnapshot.docs.map((doc) => doc.id).toSet();

      // Find users without wallets
      final usersWithoutWallets = userIds.difference(walletUserIds);
      results['usersWithoutWallets'] = usersWithoutWallets.toList();

      // Find wallets without users
      final walletsWithoutUsers = walletUserIds.difference(userIds);
      results['walletsWithoutUsers'] = walletsWithoutUsers.toList();

      // Create wallets for users without them
      final batch = _firestore.batch();
      for (final userId in usersWithoutWallets) {
        final walletRef = _firestore.collection('wallets').doc(userId);
        batch.set(walletRef, {
          'balance': 2.0, // Welcome bonus
          'createdAt': FieldValue.serverTimestamp(),
          'lastUpdated': FieldValue.serverTimestamp(),
          'totalEarnings': 2.0,
          'totalSpent': 0.0,
          'transactionCount': 1,
          'status': 'active',
        });

        // Add welcome transaction
        final transactionRef = walletRef.collection('transactions').doc();
        batch.set(transactionRef, {
          'type': 'credit',
          'amount': 2.0,
          'description': 'Welcome bonus',
          'timestamp': FieldValue.serverTimestamp(),
          'status': 'completed',
        });

        results['fixedIssues'] = (results['fixedIssues'] as int) + 1;
      }

      // Remove orphaned wallets
      for (final userId in walletsWithoutUsers) {
        final walletRef = _firestore.collection('wallets').doc(userId);
        batch.delete(walletRef);
        results['fixedIssues'] = (results['fixedIssues'] as int) + 1;
      }

      if (usersWithoutWallets.isNotEmpty || walletsWithoutUsers.isNotEmpty) {
        await batch.commit();
      }

      return results;
    } catch (e) {
      debugPrint('Error verifying data consistency: $e');
      return {
        'error': e.toString(),
        'usersWithoutWallets': <String>[],
        'walletsWithoutUsers': <String>[],
        'inconsistentData': <String>[],
        'fixedIssues': 0,
      };
    }
  }

  /// Toggle wallet status (block/unblock)
  Future<bool> toggleWalletStatus(String userId, String reason) async {
    try {
      final wallet = getWalletByUserId(userId);
      final newStatus = wallet?.isBlocked == true
          ? WalletStatus.active
          : WalletStatus.blocked;

      await _firestore.collection('wallets').doc(userId).update({
        'status': newStatus.name,
        'lastUpdated': FieldValue.serverTimestamp(),
        if (newStatus == WalletStatus.blocked) 'blockReason': reason,
        if (newStatus == WalletStatus.blocked)
          'blockedAt': FieldValue.serverTimestamp(),
        if (newStatus == WalletStatus.active)
          'blockReason': FieldValue.delete(),
        if (newStatus == WalletStatus.active) 'blockedAt': FieldValue.delete(),
      });

      return true;
    } catch (e) {
      debugPrint('Error toggling wallet status: $e');
      _errorMessage.value = 'Failed to update wallet status';
      return false;
    }
  }
}
