import 'package:flutter/material.dart';
import '../models/wallet_management_model.dart';

class WalletStatusChip extends StatelessWidget {
  final WalletStatus status;

  const WalletStatusChip({
    super.key,
    required this.status,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _getStatusColor().withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: _getStatusColor().withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: _getStatusColor(),
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 6),
          Text(
            _getStatusText(),
            style: TextStyle(
              color: _getStatusColor(),
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (status) {
      case WalletStatus.active:
        return Colors.green;
      case WalletStatus.blocked:
        return Colors.red;
    }
  }

  String _getStatusText() {
    switch (status) {
      case WalletStatus.active:
        return 'Active';
      case WalletStatus.blocked:
        return 'Blocked';
    }
  }
}
