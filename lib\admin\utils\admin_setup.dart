import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/admin_user_model.dart';

class AdminSetup {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Creates a super admin account
  /// This should be run once during initial setup
  static Future<bool> createSuperAdmin({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      // Create Firebase Auth user
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user == null) {
        throw Exception('Failed to create user');
      }

      // Create admin document
      final adminUser = AdminUserModel(
        id: credential.user!.uid,
        email: email,
        name: name,
        role: AdminRole.superAdmin,
        status: AdminStatus.active,
        createdAt: DateTime.now(),
        permissions: [
          'manage_users',
          'manage_wallets',
          'view_analytics',
          'manage_admins',
          'system_admin',
        ],
      );

      await _firestore
          .collection('admins')
          .doc(credential.user!.uid)
          .set(adminUser.toMap());

      return true;
    } catch (e) {
      debugPrint('Error creating super admin: $e');
      return false;
    }
  }

  /// Creates a regular admin account
  static Future<bool> createAdmin({
    required String email,
    required String password,
    required String name,
    required AdminRole role,
    required List<String> permissions,
  }) async {
    try {
      // Create Firebase Auth user
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user == null) {
        throw Exception('Failed to create user');
      }

      // Create admin document
      final adminUser = AdminUserModel(
        id: credential.user!.uid,
        email: email,
        name: name,
        role: role,
        status: AdminStatus.active,
        createdAt: DateTime.now(),
        permissions: permissions,
      );

      await _firestore
          .collection('admins')
          .doc(credential.user!.uid)
          .set(adminUser.toMap());

      return true;
    } catch (e) {
      debugPrint('Error creating admin: $e');
      return false;
    }
  }

  // /// Sample setup for development
  // static Future<void> setupDevelopmentAdmins() async {
  //   try {
  //     // Create super admin
  //     await createSuperAdmin(
  //       email: '<EMAIL>',
  //       password: 'SuperAdmin123!',
  //       name: 'Super Administrator',
  //     );

  //     // Create regular admin
  //     await createAdmin(
  //       email: '<EMAIL>',
  //       password: 'Admin123!',
  //       name: 'Regular Administrator',
  //       role: AdminRole.admin,
  //       permissions: [
  //         'manage_users',
  //         'manage_wallets',
  //         'view_analytics',
  //       ],
  //     );

  //     // Create moderator
  //     await createAdmin(
  //       email: '<EMAIL>',
  //       password: 'Moderator123!',
  //       name: 'Content Moderator',
  //       role: AdminRole.moderator,
  //       permissions: [
  //         'manage_users',
  //         'view_analytics',
  //       ],
  //     );

  //     debugPrint('Development admin accounts created successfully!');
  //     debugPrint('Super Admin: <EMAIL> / SuperAdmin123!');
  //     debugPrint('Admin: <EMAIL> / Admin123!');
  //     debugPrint('Moderator: <EMAIL> / Moderator123!');
  //   } catch (e) {
  //     debugPrint('Error setting up development admins: $e');
  //   }
  // }

  /// Checks if an admin exists
  static Future<bool> adminExists(String email) async {
    try {
      final query = await _firestore
          .collection('admins')
          .where('email', isEqualTo: email)
          .get();

      return query.docs.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking admin existence: $e');
      return false;
    }
  }

  /// Updates admin permissions
  static Future<bool> updateAdminPermissions(
    String adminId,
    List<String> permissions,
  ) async {
    try {
      await _firestore.collection('admins').doc(adminId).update({
        'permissions': permissions,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      debugPrint('Error updating admin permissions: $e');
      return false;
    }
  }

  /// Deactivates an admin
  static Future<bool> deactivateAdmin(String adminId) async {
    try {
      await _firestore.collection('admins').doc(adminId).update({
        'status': AdminStatus.inactive.name,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      debugPrint('Error deactivating admin: $e');
      return false;
    }
  }

  /// Available permissions list
  static const List<String> availablePermissions = [
    'manage_users',
    'manage_wallets',
    'view_analytics',
    'manage_admins',
    'system_admin',
    'manage_posts',
    'manage_categories',
    'view_logs',
    'export_data',
  ];

  /// Permission descriptions
  static const Map<String, String> permissionDescriptions = {
    'manage_users': 'Create, edit, block/unblock user accounts',
    'manage_wallets': 'View and manage user wallets and transactions',
    'view_analytics': 'Access analytics and reporting features',
    'manage_admins': 'Create and manage other admin accounts',
    'system_admin': 'Full system administration access',
    'manage_posts': 'Moderate and manage user posts',
    'manage_categories': 'Create and manage post categories',
    'view_logs': 'Access system and audit logs',
    'export_data': 'Export data and generate reports',
  };
}
