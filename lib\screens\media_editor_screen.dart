import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

class MediaEditorScreen extends StatefulWidget {
  final XFile mediaFile;
  final bool isVideo;
  final Function(XFile) onSave;

  const MediaEditorScreen({
    super.key,
    required this.mediaFile,
    required this.isVideo,
    required this.onSave,
  });

  @override
  State<MediaEditorScreen> createState() => _MediaEditorScreenState();
}

class _MediaEditorScreenState extends State<MediaEditorScreen> {
  late XFile _editedFile;
  double _brightness = 0.0;
  double _contrast = 1.0;
  double _saturation = 1.0;

  @override
  void initState() {
    super.initState();
    _editedFile = widget.mediaFile;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: Text(widget.isVideo ? 'Edit Video' : 'Edit Image'),
        actions: [
          TextButton(
            onPressed: _saveChanges,
            child: const Text(
              'Save',
              style: TextStyle(
                color: Colors.blue,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Media Preview
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: Colors.grey[900],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: _buildMediaPreview(),
              ),
            ),
          ),

          // Editing Controls
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Adjustments',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 20),
                  
                  if (!widget.isVideo) ...[
                    // Image editing controls
                    _buildSlider(
                      'Brightness',
                      _brightness,
                      -1.0,
                      1.0,
                      (value) => setState(() => _brightness = value),
                    ),
                    _buildSlider(
                      'Contrast',
                      _contrast,
                      0.0,
                      2.0,
                      (value) => setState(() => _contrast = value),
                    ),
                    _buildSlider(
                      'Saturation',
                      _saturation,
                      0.0,
                      2.0,
                      (value) => setState(() => _saturation = value),
                    ),
                  ] else ...[
                    // Video editing controls
                    const Text(
                      'Video editing features coming soon!',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 20),
                    _buildActionButton(
                      'Trim Video',
                      Icons.content_cut,
                      () => _showComingSoon('Video trimming'),
                    ),
                    const SizedBox(height: 12),
                    _buildActionButton(
                      'Add Filters',
                      Icons.filter,
                      () => _showComingSoon('Video filters'),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMediaPreview() {
    if (widget.isVideo) {
      return Container(
        color: Colors.grey[800],
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.play_circle_filled,
                size: 64,
                color: Colors.white70,
              ),
              SizedBox(height: 16),
              Text(
                'Video Preview',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return ColorFiltered(
      colorFilter: ColorFilter.matrix([
        _contrast, 0, 0, 0, _brightness * 255,
        0, _contrast, 0, 0, _brightness * 255,
        0, 0, _contrast, 0, _brightness * 255,
        0, 0, 0, 1, 0,
      ]),
      child: Image.file(
        File(widget.mediaFile.path),
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Colors.grey[800],
            child: const Center(
              child: Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.white54,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSlider(
    String label,
    double value,
    double min,
    double max,
    Function(double) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Slider(
          value: value,
          min: min,
          max: max,
          activeColor: Colors.blue,
          inactiveColor: Colors.grey[600],
          onChanged: onChanged,
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildActionButton(String label, IconData icon, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.grey[800],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[600]!),
        ),
        child: Row(
          children: [
            Icon(icon, color: Colors.white70, size: 20),
            const SizedBox(width: 12),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const Spacer(),
            const Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature coming soon!'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _saveChanges() {
    // For now, just return the original file
    // In a real implementation, you would apply the edits and save a new file
    widget.onSave(_editedFile);
    Navigator.pop(context);
  }
}
