import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/post_service.dart';
import '../controllers/poll_controller.dart';
import 'poll_results_widget.dart';

/// A reusable widget for displaying and interacting with polls
class PollWidget extends StatefulWidget {
  final Post post;
  final bool isDetailView;

  const PollWidget({
    super.key,
    required this.post,
    this.isDetailView = false,
  });

  @override
  State<PollWidget> createState() => _PollWidgetState();
}

class _PollWidgetState extends State<PollWidget> {
  bool _hasInitialized = false;
  String? _lastPostId;

  @override
  Widget build(BuildContext context) {
    if (!widget.post.hasPoll) return const SizedBox.shrink();

    // Initialize poll controller if not already done
    try {
      Get.find<PollController>();
    } catch (e) {
      Get.put(PollController());
    }

    return GetBuilder<PollController>(
      id: 'poll_${widget.post.id}', // Specific ID for this poll
      builder: (pollController) {
        // Only update poll data once per post to prevent repeated calls
        if (!_hasInitialized || _lastPostId != widget.post.id) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              pollController.updatePollData(widget.post);
              setState(() {
                _hasInitialized = true;
                _lastPostId = widget.post.id;
              });
            }
          });
        }

        return _buildPollContent(pollController);
      },
    );
  }

  Widget _buildPollContent(PollController pollController) {
    final totalVotes = pollController.getTotalVotes(widget.post.id);
    final currentUser = FirebaseAuth.instance.currentUser;

    // Check both controller and post data for voting status
    final hasVotedInController =
        currentUser != null && pollController.hasUserVoted(widget.post.id);
    final hasVotedInPost =
        currentUser != null && widget.post.hasUserVoted(currentUser.uid);
    final hasVoted = hasVotedInController || hasVotedInPost;

    final userVote = pollController.getUserVote(widget.post.id) ??
        (currentUser != null ? widget.post.getUserVote(currentUser.uid) : null);

    // Remove excessive debug logging - only log when there are actual changes
    // debugPrint('Poll ${widget.post.id}: hasVoted = $hasVoted, userVote = $userVote');
    // debugPrint('Poll ${widget.post.id}: hasVotedInController = $hasVotedInController, hasVotedInPost = $hasVotedInPost');

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.poll, color: Colors.blue[600], size: 16),
              const SizedBox(width: 6),
              Text(
                hasVoted ? 'Poll Results' : 'Poll',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.blue[700],
                  fontSize: 13,
                ),
              ),
              const SizedBox(width: 8),
              if (hasVoted)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.green[200]!),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.check_circle,
                          size: 12, color: Colors.green[600]),
                      const SizedBox(width: 3),
                      Text(
                        'You voted',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.green[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              const Spacer(),
              Text(
                '$totalVotes vote${totalVotes != 1 ? 's' : ''}',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),

          // Show results if user has voted, otherwise show voting options
          if (hasVoted && userVote != null) ...[
            // Compact poll results in one row
            Row(
              children: [
                Expanded(
                  child: PollResultsWidget(
                    votes: pollController.getOptimisticVotes(widget.post.id) ??
                        Map<String, int>.from(widget.post.pollVotes),
                    userVote: userVote,
                    size: 20, // Reduced size since we're using line chart
                  ),
                ),
              ],
            ),
          ] else ...[
            Row(
              children: [
                Expanded(
                  child: _buildPollOption(
                    pollController,
                    'yes',
                    'Yes',
                    Icons.thumb_up,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildPollOption(
                    pollController,
                    'no',
                    'No',
                    Icons.thumb_down,
                    Colors.red,
                  ),
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: _buildPollOption(
                    pollController,
                    'dont_care',
                    "Don't care",
                    Icons.sentiment_neutral,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPollOption(
    PollController pollController,
    String optionKey,
    String optionText,
    IconData icon,
    Color color,
  ) {
    final votes = pollController.getVoteCount(widget.post.id, optionKey);
    final percentage =
        pollController.getVotePercentage(widget.post.id, optionKey);
    final isSelected = pollController.getUserVote(widget.post.id) == optionKey;
    final currentUser = FirebaseAuth.instance.currentUser;
    final hasVoted =
        currentUser != null && pollController.hasUserVoted(widget.post.id);
    final isVoting = pollController.isVoting(widget.post.id);
    final canVote = currentUser != null && !hasVoted && !isVoting;
    final totalVotes = pollController.getTotalVotes(widget.post.id);

    return GestureDetector(
      onTap: canVote
          ? () {
              // Fire and forget - let the controller handle all async operations
              pollController.voteOnPoll(widget.post.id, optionKey);
            }
          : null,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.1) : Colors.white,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Stack(
          children: [
            // Progress bar background
            if (totalVotes > 0)
              Positioned.fill(
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: percentage / 100,
                  child: Container(
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              ),
            // Content
            Row(
              children: [
                Icon(
                  icon,
                  size: 16,
                  color: isSelected ? color : Colors.grey[600],
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    optionText,
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.normal,
                      color: isSelected ? color : Colors.grey[700],
                    ),
                  ),
                ),
                if (totalVotes > 0) ...[
                  Text(
                    '${percentage.toStringAsFixed(0)}%',
                    style: TextStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                Text(
                  votes.toString(),
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                    color: isSelected ? color : Colors.grey[600],
                  ),
                ),
                if (isSelected) ...[
                  const SizedBox(width: 4),
                  Icon(
                    Icons.check_circle,
                    size: 14,
                    color: color,
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }
}
