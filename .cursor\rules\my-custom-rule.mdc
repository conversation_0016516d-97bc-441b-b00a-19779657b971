---
description: 
globs: 
alwaysApply: true
---
Technology Stack Overview

🖥 Frontend / Application Layer

Flutter 3.x (Dart): Single codebase supporting Android, iOS, Web, macOS, Windows, and Linux.

UI Components: Built using Material 3 widgets and custom stateless/stateful widgets located in lib/screens and lib/widgets.

☁️ Cloud Services (Firebase)

Firebase Core: Initialized in main.dart.

Firebase Authentication: Supports email/password sign-up, login, verification, and password reset workflows.

Cloud Firestore: Used only for storing user profile documents at users/{uid}. Fields include:

emailVerified, profileCompleted, lastLogin, username, etc.

Firebase Storage: Not actively used yet; storage.rules are defined for future file/image uploads.

💳 Payments (Production-Ready)

Stripe Integration: Uses flutter_stripe package with real Stripe API integration.

Backend Endpoint: Live Firebase Functions at https://us-central1-money-mouthy.cloudfunctions.net

All payment operations use real Stripe processing with Firebase Functions backend.

📦 Data Persistence (Firebase + Local)

Wallet & Transactions:

All Platforms: Stored in Firebase Firestore with real-time synchronization.

Local caching via GetX reactive state management.

Posts: Stored in Firebase Firestore with real-time updates.

User Data: Stored in Firestore under the users collection.

Wallet Balance: Stored in Firestore wallets/{uid} collection with real-time sync.

🛠 Tooling & DevOps

Common packages: intl, flutter_stripe, cloud_firestore, firebase_auth, shared_preferences, sqflite, path, http.

Native Android/iOS folders are included (generated via flutter create).

Firebase security rules (firestore.rules, storage.rules) are included but not yet fully enforced.

📊 Database Summary

User Data: Stored in Firestore under the users collection.

Posts & Wallet: Stored in Firebase Firestore with real-time synchronization.

Full Firebase integration implemented for production use with real Stripe payments.