import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:image_network/image_network.dart';

class ImageGalleryScreen extends StatefulWidget {
  final List<String> imageUrls;
  final List<String> videoUrls;
  final int initialIndex;
  final String postTitle;
  final String authorName;

  const ImageGalleryScreen({
    super.key,
    required this.imageUrls,
    this.videoUrls = const [],
    this.initialIndex = 0,
    required this.postTitle,
    required this.authorName,
  });

  @override
  State<ImageGalleryScreen> createState() => _ImageGalleryScreenState();
}

class _ImageGalleryScreenState extends State<ImageGalleryScreen> {
  late PageController _pageController;
  late int _currentIndex;
  bool _isControlsVisible = true;
  List<String> _allMediaUrls = [];
  List<bool> _isVideoList = [];

  @override
  void initState() {
    super.initState();

    // Combine images and videos into a single list
    _allMediaUrls = [...widget.imageUrls, ...widget.videoUrls];
    _isVideoList = [
      ...List.filled(widget.imageUrls.length, false),
      ...List.filled(widget.videoUrls.length, true),
    ];

    // Ensure initial index is within bounds
    _currentIndex = widget.initialIndex.clamp(0, _allMediaUrls.length - 1);
    _pageController = PageController(initialPage: _currentIndex);

    // Hide system UI for immersive experience
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

    // Auto-hide controls after 3 seconds
    _autoHideControls();
  }

  @override
  void dispose() {
    _pageController.dispose();
    // Restore system UI
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    super.dispose();
  }

  void _autoHideControls() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _isControlsVisible = false;
        });
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _isControlsVisible = !_isControlsVisible;
    });
    if (_isControlsVisible) {
      _autoHideControls();
    }
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: _toggleControls,
        child: Stack(
          children: [
            // Media Gallery
            PageView.builder(
              controller: _pageController,
              onPageChanged: _onPageChanged,
              itemCount: _allMediaUrls.length,
              itemBuilder: (context, index) {
                return _buildMediaItem(index);
              },
            ),

            // Controls Overlay
            if (_isControlsVisible) _buildControlsOverlay(),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaItem(int index) {
    final mediaUrl = _allMediaUrls[index];
    final isVideo = _isVideoList[index];

    if (isVideo) {
      return _buildVideoItem(mediaUrl);
    } else {
      return _buildImageItem(mediaUrl);
    }
  }

  Widget _buildImageItem(String imageUrl) {
    return Center(
      child: InteractiveViewer(
        minScale: 0.5,
        maxScale: 3.0,
        child: kIsWeb
            ? ImageNetwork(
                image: imageUrl,
                height: 200,
                width: 400,
                duration: 500,
                curve: Curves.easeIn,
                onPointer: true,
                debugPrint: false,
                backgroundColor: Colors.black,
                fitAndroidIos: BoxFit.contain,
                fitWeb: BoxFitWeb.contain,
                onLoading: const CircularProgressIndicator(
                  color: Colors.indigoAccent,
                  strokeWidth: 0.1,
                ),
                onError: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.white54,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'Failed to load image',
                        style: TextStyle(color: Colors.white54, fontSize: 16),
                      ),
                    ],
                  ),
                ),
              )
            : Image.network(
                imageUrl,
                fit: BoxFit.contain,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) {
                    return child;
                  }
                  return Center(
                    child: CircularProgressIndicator(
                      value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                      color: Colors.white,
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.white54,
                        ),
                        SizedBox(height: 16),
                        Text(
                          'Failed to load image',
                          style: TextStyle(
                            color: Colors.white54,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
      ),
    );
  }

  Widget _buildVideoItem(String videoUrl) {
    return Center(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[900],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.play_circle_filled,
              size: 80,
              color: Colors.white.withOpacity(0.8),
            ),
            const SizedBox(height: 16),
            const Text(
              'Tap to play video',
              style: TextStyle(color: Colors.white70, fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildControlsOverlay() {
    return SafeArea(
      child: Column(
        children: [
          // Top Controls
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withOpacity(0.7),
                  Colors.transparent,
                ],
              ),
            ),
            child: Row(
              children: [
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.postTitle,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        'by ${widget.authorName}',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () {
                    // Add share functionality
                  },
                  icon: const Icon(Icons.share, color: Colors.white, size: 24),
                ),
              ],
            ),
          ),

          const Spacer(),

          // Bottom Controls
          if (_allMediaUrls.length > 1)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    Colors.black.withOpacity(0.7),
                    Colors.transparent,
                  ],
                ),
              ),
              child: Column(
                children: [
                  // Media counter
                  Text(
                    '${_currentIndex + 1} of ${_allMediaUrls.length}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Thumbnail strip
                  _buildThumbnailStrip(),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildThumbnailStrip() {
    return SizedBox(
      height: 60,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _allMediaUrls.length,
        itemBuilder: (context, index) {
          final isSelected = index == _currentIndex;
          final isVideo = _isVideoList[index];

          return GestureDetector(
            onTap: () {
              _pageController.animateToPage(
                index,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            },
            child: Container(
              width: 60,
              height: 60,
              margin: EdgeInsets.only(
                right: index < _allMediaUrls.length - 1 ? 8 : 0,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected ? Colors.white : Colors.transparent,
                  width: 2,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child: isVideo
                    ? Container(
                        color: Colors.grey[800],
                        child: const Icon(
                          Icons.play_circle_filled,
                          color: Colors.white,
                          size: 24,
                        ),
                      )
                    : kIsWeb
                        ? ImageNetwork(
                            image: _allMediaUrls[index],
                            height: 60,
                            width: 60,
                            duration: 500,
                            curve: Curves.easeIn,
                            onPointer: true,
                            debugPrint: false,
                            backgroundColor: Colors.grey[800]!,
                            fitAndroidIos: BoxFit.cover,
                            fitWeb: BoxFitWeb.cover,
                            onLoading: const CircularProgressIndicator(
                              color: Colors.indigoAccent,
                              strokeWidth: 0.1,
                            ),
                            onError: Container(
                              color: Colors.grey[800],
                              child: const Icon(
                                Icons.image,
                                color: Colors.white54,
                                size: 24,
                              ),
                            ),
                          )
                        : kIsWeb
                            ? ImageNetwork(
                                image: _allMediaUrls[index],
                                height: 60,
                                width: 60,
                                duration: 500,
                                curve: Curves.easeIn,
                                onPointer: true,
                                debugPrint: false,
                                backgroundColor: Colors.grey[800]!,
                                fitAndroidIos: BoxFit.cover,
                                fitWeb: BoxFitWeb.cover,
                                onLoading: Container(
                                  color: Colors.grey[800],
                                  child: const Center(
                                    child: CircularProgressIndicator(
                                      color: Colors.indigoAccent,
                                      strokeWidth: 0.1,
                                    ),
                                  ),
                                ),
                                onError: Container(
                                  color: Colors.grey[800],
                                  child: const Icon(
                                    Icons.image,
                                    color: Colors.white54,
                                    size: 24,
                                  ),
                                ),
                              )
                            : Image.network(
                                _allMediaUrls[index],
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    color: Colors.grey[800],
                                    child: const Icon(
                                      Icons.image,
                                      color: Colors.white54,
                                      size: 24,
                                    ),
                                  );
                                },
                              ),
              ),
            ),
          );
        },
      ),
    );
  }
}
