import 'package:flutter/material.dart';
import '../theme/admin_theme.dart';

class AdminBottomSheet extends StatelessWidget {
  final String title;
  final Widget child;
  final List<Widget>? actions;
  final bool isScrollable;
  final double? height;
  final EdgeInsets? padding;

  const AdminBottomSheet({
    super.key,
    required this.title,
    required this.child,
    this.actions,
    this.isScrollable = true,
    this.height,
    this.padding,
  });

  static Future<T?> show<T>({
    required BuildContext context,
    required String title,
    required Widget child,
    List<Widget>? actions,
    bool isScrollable = true,
    double? height,
    EdgeInsets? padding,
    bool isDismissible = true,
    bool enableDrag = true,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isScrollControlled: true,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      backgroundColor: Colors.transparent,
      useSafeArea: true,
      builder: (context) => AdminBottomSheet(
        title: title,
        actions: actions,
        isScrollable: isScrollable,
        height: height,
        padding: padding,
        child: child,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isMobile = screenWidth < 768;

    // More conservative height calculation to prevent overflow
    final maxHeight = isMobile ? screenHeight * 0.8 : screenHeight * 0.9;
    final effectiveHeight = height ?? maxHeight;
    final safeHeight = (effectiveHeight).clamp(0.0, screenHeight - 100);

    return GestureDetector(
      onTap: () {}, // Prevent tap-through and handle gestures properly
      child: Container(
        constraints: BoxConstraints(
          maxHeight: safeHeight,
          minHeight: 200,
        ),
        decoration: const BoxDecoration(
          color: AdminTheme.surfaceColor,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AdminTheme.radiusLarge),
            topRight: Radius.circular(AdminTheme.radiusLarge),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: AdminTheme.spacingSmall),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AdminTheme.borderColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: isMobile
                    ? AdminTheme.spacingMedium
                    : AdminTheme.spacingLarge,
                vertical: isMobile
                    ? AdminTheme.spacingSmall
                    : AdminTheme.spacingMedium,
              ),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: AdminTheme.borderColor),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      title,
                      style: (isMobile
                              ? AdminTheme.bodyLarge
                              : AdminTheme.headingSmall)
                          .copyWith(
                        fontWeight: FontWeight.w600,
                        color: AdminTheme.textPrimary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(Icons.close, size: isMobile ? 20 : 24),
                    style: IconButton.styleFrom(
                      backgroundColor: AdminTheme.borderColor.withOpacity(0.5),
                      foregroundColor: AdminTheme.textSecondary,
                      minimumSize: Size(isMobile ? 32 : 40, isMobile ? 32 : 40),
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: isScrollable
                  ? SingleChildScrollView(
                      physics: const ClampingScrollPhysics(),
                      padding: EdgeInsets.all(
                        isMobile
                            ? AdminTheme.spacingMedium
                            : AdminTheme.spacingLarge,
                      ),
                      child: MouseRegion(
                        child: child,
                      ),
                    )
                  : Padding(
                      padding: EdgeInsets.all(
                        isMobile
                            ? AdminTheme.spacingMedium
                            : AdminTheme.spacingLarge,
                      ),
                      child: MouseRegion(
                        child: child,
                      ),
                    ),
            ),

            // Actions
            if (actions != null && actions!.isNotEmpty)
              Container(
                padding: EdgeInsets.all(
                  isMobile ? AdminTheme.spacingMedium : AdminTheme.spacingLarge,
                ),
                decoration: const BoxDecoration(
                  border: Border(
                    top: BorderSide(color: AdminTheme.borderColor),
                  ),
                ),
                child: isMobile
                    ? Column(
                        children: actions!
                            .map((action) => Container(
                                  width: double.infinity,
                                  margin: const EdgeInsets.only(
                                      bottom: AdminTheme.spacingSmall),
                                  child: action,
                                ))
                            .toList(),
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: actions!
                            .map((action) => Padding(
                                  padding: const EdgeInsets.only(
                                      left: AdminTheme.spacingMedium),
                                  child: action,
                                ))
                            .toList(),
                      ),
              ),

            // Safe area for keyboard
            if (keyboardHeight > 0) SizedBox(height: keyboardHeight),
          ],
        ),
      ),
    );
  }
}

// Specialized bottom sheets for common use cases
class FormBottomSheet extends StatelessWidget {
  final String title;
  final List<Widget> fields;
  final VoidCallback? onSave;
  final VoidCallback? onCancel;
  final String saveText;
  final String cancelText;
  final bool isLoading;

  const FormBottomSheet({
    super.key,
    required this.title,
    required this.fields,
    this.onSave,
    this.onCancel,
    this.saveText = 'Save',
    this.cancelText = 'Cancel',
    this.isLoading = false,
  });

  static Future<T?> show<T>({
    required BuildContext context,
    required String title,
    required List<Widget> fields,
    VoidCallback? onSave,
    VoidCallback? onCancel,
    String saveText = 'Save',
    String cancelText = 'Cancel',
    bool isLoading = false,
  }) {
    return AdminBottomSheet.show<T>(
      context: context,
      title: title,
      child: FormBottomSheet(
        title: title,
        fields: fields,
        onSave: onSave,
        onCancel: onCancel,
        saveText: saveText,
        cancelText: cancelText,
        isLoading: isLoading,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          ...fields.map((field) => Padding(
                padding:
                    const EdgeInsets.only(bottom: AdminTheme.spacingMedium),
                child: MouseRegion(child: field),
              )),
          const SizedBox(height: AdminTheme.spacingLarge),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: isLoading
                      ? null
                      : (onCancel ?? () => Navigator.of(context).pop()),
                  style: AdminTheme.secondaryButtonStyle,
                  child: Text(cancelText),
                ),
              ),
              const SizedBox(width: AdminTheme.spacingMedium),
              Expanded(
                child: ElevatedButton(
                  onPressed: isLoading ? null : onSave,
                  style: AdminTheme.primaryButtonStyle,
                  child: isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                                AdminTheme.textOnPrimary),
                          ),
                        )
                      : Text(saveText),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class ConfirmationBottomSheet extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;
  final String confirmText;
  final String cancelText;
  final Color? confirmColor;
  final IconData? icon;

  const ConfirmationBottomSheet({
    super.key,
    required this.title,
    required this.message,
    this.onConfirm,
    this.onCancel,
    this.confirmText = 'Confirm',
    this.cancelText = 'Cancel',
    this.confirmColor,
    this.icon,
  });

  static Future<bool?> show({
    required BuildContext context,
    required String title,
    required String message,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
    Color? confirmColor,
    IconData? icon,
  }) {
    return AdminBottomSheet.show<bool>(
      context: context,
      title: title,
      height: 300,
      child: ConfirmationBottomSheet(
        title: title,
        message: message,
        onConfirm: onConfirm,
        onCancel: onCancel,
        confirmText: confirmText,
        cancelText: cancelText,
        confirmColor: confirmColor,
        icon: icon,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (icon != null) ...[
            Container(
              padding: const EdgeInsets.all(AdminTheme.spacingLarge),
              decoration: BoxDecoration(
                color:
                    (confirmColor ?? AdminTheme.primaryColor).withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 48,
                color: confirmColor ?? AdminTheme.primaryColor,
              ),
            ),
            const SizedBox(height: AdminTheme.spacingLarge),
          ],
          Text(
            message,
            style: AdminTheme.bodyLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AdminTheme.spacingXLarge),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: onCancel ?? () => Navigator.of(context).pop(false),
                  style: AdminTheme.secondaryButtonStyle,
                  child: Text(cancelText),
                ),
              ),
              const SizedBox(width: AdminTheme.spacingMedium),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop(true);
                    onConfirm?.call();
                  },
                  style: confirmColor != null
                      ? ElevatedButton.styleFrom(
                          backgroundColor: confirmColor,
                          foregroundColor: AdminTheme.textOnPrimary,
                        )
                      : AdminTheme.primaryButtonStyle,
                  child: Text(confirmText),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
