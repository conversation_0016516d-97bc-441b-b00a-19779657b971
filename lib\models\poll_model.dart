/// Poll-related data models for the Money Mouthy app
///
/// This file contains models for handling poll functionality including
/// poll options, votes, and poll data structures.
library;

/// Enum for the three fixed poll options
enum PollOption {
  yes('yes', 'Yes'),
  no('no', 'No'),
  dontCare('dont_care', "Don't care");

  const PollOption(this.value, this.displayName);

  final String value;
  final String displayName;

  static PollOption fromValue(String value) {
    return PollOption.values.firstWhere(
      (option) => option.value == value,
      orElse: () => PollOption.dontCare,
    );
  }

  static List<PollOption> get allOptions => PollOption.values;
}

/// Model representing a single vote in a poll
class PollVote {
  final String userId;
  final String option; // 'yes', 'no', or 'dont_care'
  final DateTime timestamp;

  const PollVote({
    required this.userId,
    required this.option,
    required this.timestamp,
  });

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'option': option,
      'timestamp': timestamp.millisecondsSinceEpoch,
    };
  }

  factory PollVote.fromMap(Map<String, dynamic> map) {
    return PollVote(
      userId: map['userId'] ?? '',
      option: map['option'] ?? '',
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp'] ?? 0),
    );
  }

  PollVote copyWith({
    String? userId,
    String? option,
    DateTime? timestamp,
  }) {
    return PollVote(
      userId: userId ?? this.userId,
      option: option ?? this.option,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PollVote &&
        other.userId == userId &&
        other.option == option &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode => userId.hashCode ^ option.hashCode ^ timestamp.hashCode;
}

/// Model representing poll data and statistics
class Poll {
  final Map<String, int> votes; // Vote counts for each option
  final Map<String, List<String>> voters; // Users who voted for each option
  final DateTime? createdAt;

  const Poll({
    this.votes = const {},
    this.voters = const {},
    this.createdAt,
  });

  /// Create a new empty poll with default structure
  factory Poll.empty() {
    return Poll(
      votes: {
        PollOption.yes.value: 0,
        PollOption.no.value: 0,
        PollOption.dontCare.value: 0,
      },
      voters: {
        PollOption.yes.value: <String>[],
        PollOption.no.value: <String>[],
        PollOption.dontCare.value: <String>[],
      },
      createdAt: DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'votes': votes,
      'voters': voters,
      'createdAt': createdAt?.millisecondsSinceEpoch,
    };
  }

  factory Poll.fromMap(Map<String, dynamic> map) {
    return Poll(
      votes: Map<String, int>.from(map['votes'] ?? {}),
      voters: Map<String, List<String>>.from(
        (map['voters'] ?? {}).map<String, List<String>>(
          (key, value) => MapEntry(key, List<String>.from(value ?? [])),
        ),
      ),
      createdAt: map['createdAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['createdAt'])
          : null,
    );
  }

  Poll copyWith({
    Map<String, int>? votes,
    Map<String, List<String>>? voters,
    DateTime? createdAt,
  }) {
    return Poll(
      votes: votes ?? this.votes,
      voters: voters ?? this.voters,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// Get total number of votes across all options
  int get totalVotes {
    return votes.values.fold(0, (total, count) => total + count);
  }

  /// Check if a user has voted
  bool hasUserVoted(String userId) {
    return voters.values.any((voterList) => voterList.contains(userId));
  }

  /// Get the option a user voted for
  String? getUserVote(String userId) {
    for (final entry in voters.entries) {
      if (entry.value.contains(userId)) {
        return entry.key;
      }
    }
    return null;
  }

  /// Get vote percentage for an option
  double getVotePercentage(String option) {
    if (totalVotes == 0) return 0.0;
    final optionVotes = votes[option] ?? 0;
    return (optionVotes / totalVotes) * 100;
  }

  /// Add a vote for a user
  Poll addVote(String userId, String option) {
    // Remove user from any existing votes first
    final newVoters = Map<String, List<String>>.from(voters);
    for (final key in newVoters.keys) {
      newVoters[key] = List<String>.from(newVoters[key]!)..remove(userId);
    }

    // Add user to the new option
    if (!newVoters.containsKey(option)) {
      newVoters[option] = <String>[];
    }
    newVoters[option] = List<String>.from(newVoters[option]!)..add(userId);

    // Update vote counts
    final newVotes = Map<String, int>.from(votes);
    for (final key in newVotes.keys) {
      newVotes[key] = newVoters[key]?.length ?? 0;
    }

    return copyWith(
      votes: newVotes,
      voters: newVoters,
    );
  }

  /// Remove a vote from a user
  Poll removeVote(String userId) {
    final newVoters = Map<String, List<String>>.from(voters);
    for (final key in newVoters.keys) {
      newVoters[key] = List<String>.from(newVoters[key]!)..remove(userId);
    }

    // Update vote counts
    final newVotes = Map<String, int>.from(votes);
    for (final key in newVotes.keys) {
      newVotes[key] = newVoters[key]?.length ?? 0;
    }

    return copyWith(
      votes: newVotes,
      voters: newVoters,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Poll &&
        other.votes.toString() == votes.toString() &&
        other.voters.toString() == voters.toString() &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode => votes.hashCode ^ voters.hashCode ^ createdAt.hashCode;
}
