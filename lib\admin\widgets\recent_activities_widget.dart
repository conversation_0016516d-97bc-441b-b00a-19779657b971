import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/user_activity_controller.dart';
import '../models/user_management_model.dart';

class RecentActivitiesWidget extends StatelessWidget {
  const RecentActivitiesWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final activityController = Get.find<UserActivityController>();
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Activities',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              TextButton(
                onPressed: () {
                  Get.toNamed('/admin/activity');
                },
                child: const Text('View All'),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Activity List
          Obx(() {
            final recentActivities =
                activityController.activities.take(6).toList();

            if (recentActivities.isEmpty) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(32.0),
                  child: Text('No recent activities'),
                ),
              );
            }

            return ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: recentActivities.length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final activity = recentActivities[index];
                return _buildActivityItem(activity);
              },
            );
          }),
        ],
      ),
    );
  }

  Widget _buildActivityItem(UserActivityModel activity) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        children: [
          // Activity Icon
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getActivityColor(activity.type).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getActivityIcon(activity.type),
              color: _getActivityColor(activity.type),
              size: 20,
            ),
          ),
          const SizedBox(width: 16),

          // Activity Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getActivityTitle(activity.type),
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  activity.description,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 13,
                  ),
                ),
              ],
            ),
          ),

          // Timestamp
          Text(
            _formatTimeAgo(activity.timestamp),
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Color _getActivityColor(UserActivityType type) {
    switch (type) {
      case UserActivityType.login:
        return Colors.green;
      case UserActivityType.logout:
        return Colors.orange;
      case UserActivityType.postCreated:
        return Colors.blue;
      case UserActivityType.postDeleted:
        return Colors.red;
      case UserActivityType.walletTransaction:
        return Colors.purple;
      case UserActivityType.profileUpdate:
        return Colors.teal;
      case UserActivityType.passwordChange:
        return Colors.indigo;
    }
  }

  IconData _getActivityIcon(UserActivityType type) {
    switch (type) {
      case UserActivityType.login:
        return Icons.login;
      case UserActivityType.logout:
        return Icons.logout;
      case UserActivityType.postCreated:
        return Icons.post_add;
      case UserActivityType.postDeleted:
        return Icons.delete;
      case UserActivityType.walletTransaction:
        return Icons.account_balance_wallet;
      case UserActivityType.profileUpdate:
        return Icons.edit;
      case UserActivityType.passwordChange:
        return Icons.lock;
    }
  }

  String _getActivityTitle(UserActivityType type) {
    switch (type) {
      case UserActivityType.login:
        return 'User Login';
      case UserActivityType.logout:
        return 'User Logout';
      case UserActivityType.postCreated:
        return 'Post Created';
      case UserActivityType.postDeleted:
        return 'Post Deleted';
      case UserActivityType.walletTransaction:
        return 'Wallet Transaction';
      case UserActivityType.profileUpdate:
        return 'Profile Updated';
      case UserActivityType.passwordChange:
        return 'Password Changed';
    }
  }

  String _formatTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} min ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    }
  }
}
