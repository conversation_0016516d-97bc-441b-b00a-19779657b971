# Firebase Cloud Functions - Post Cleanup Deployment Guide

## 🚀 Functions Added

### 1. **cleanupOldPosts** (Scheduled Function)
- **Schedule**: Runs every hour (`0 * * * *`)
- **Purpose**: Automatically deletes posts older than 48 hours
- **Batch Size**: 500 posts per run (to avoid timeouts)
- **Timezone**: UTC

### 2. **manualCleanupOldPosts** (HTTP Function)
- **Purpose**: Manual trigger for testing cleanup
- **Method**: POST/GET
- **URL**: `https://YOUR_REGION-YOUR_PROJECT.cloudfunctions.net/manualCleanupOldPosts`

### 3. **getCleanupStats** (HTTP Function)
- **Purpose**: Get statistics about posts and cleanup status
- **Method**: GET
- **URL**: `https://YOUR_REGION-YOUR_PROJECT.cloudfunctions.net/getCleanupStats`

## 📋 Deployment Steps

### 1. Install Firebase CLI (if not already installed)
```bash
npm install -g firebase-tools
```

### 2. Login to Firebase
```bash
firebase login
```

### 3. Navigate to functions directory
```bash
cd functions
```

### 4. Install dependencies (if needed)
```bash
npm install
```

### 5. Deploy the functions
```bash
# Deploy all functions
firebase deploy --only functions

# Or deploy specific functions
firebase deploy --only functions:cleanupOldPosts,functions:manualCleanupOldPosts,functions:getCleanupStats
```

## 🔧 Configuration

### Required Permissions
The functions need these Firestore permissions:
- Read access to `posts` collection
- Delete access to `posts` collection
- Query access with timestamp filters

### Environment Setup
No additional environment variables needed - uses existing Firebase Admin SDK.

## 🧪 Testing

### 1. Test Manual Cleanup
```bash
# Using curl
curl -X POST https://YOUR_REGION-YOUR_PROJECT.cloudfunctions.net/manualCleanupOldPosts

# Using browser (GET request)
https://YOUR_REGION-YOUR_PROJECT.cloudfunctions.net/manualCleanupOldPosts
```

### 2. Check Cleanup Stats
```bash
# Using curl
curl https://YOUR_REGION-YOUR_PROJECT.cloudfunctions.net/getCleanupStats

# Using browser
https://YOUR_REGION-YOUR_PROJECT.cloudfunctions.net/getCleanupStats
```

### 3. Monitor Scheduled Function
- Go to Firebase Console → Functions
- Check logs for `cleanupOldPosts` function
- Should run every hour automatically

## 📊 Expected Response Examples

### Manual Cleanup Response
```json
{
  "message": "Manual cleanup completed successfully",
  "deletedCount": 25,
  "cutoffTime": "2024-01-13T10:00:00.000Z",
  "success": true
}
```

### Cleanup Stats Response
```json
{
  "totalPosts": 150,
  "postsLast24Hours": 45,
  "postsLast48Hours": 89,
  "postsOlderThan48Hours": 61,
  "nextCleanupWillDelete": 61,
  "cutoffTime": "2024-01-13T10:00:00.000Z",
  "timestamp": "2024-01-15T10:00:00.000Z",
  "success": true
}
```

## 🔍 Monitoring

### Function Logs
```bash
# View logs for scheduled cleanup
firebase functions:log --only cleanupOldPosts

# View all function logs
firebase functions:log
```

### Firebase Console
- Go to Firebase Console → Functions
- Monitor execution count, errors, and performance
- Check Cloud Scheduler for scheduled function status

## ⚠️ Important Notes

1. **Real-time Updates**: When posts are deleted by the function, the Flutter app will automatically reflect changes due to real-time listeners.

2. **Batch Processing**: Function processes 500 posts per run to avoid timeouts. If you have more than 500 old posts, multiple runs will be needed.

3. **Error Handling**: Functions include comprehensive error handling and won't crash the app if something goes wrong.

4. **Cost Optimization**: Scheduled function runs every hour but only processes when there are old posts to delete.

5. **Testing**: Use manual trigger function for testing before relying on scheduled cleanup.

## 🚨 Troubleshooting

### Function Not Running
- Check Cloud Scheduler is enabled in Google Cloud Console
- Verify function deployment was successful
- Check function logs for errors

### Permission Errors
- Ensure Firebase Admin SDK has proper Firestore permissions
- Check IAM roles in Google Cloud Console

### Timeout Issues
- Function is configured with 500 post batch limit
- If still timing out, reduce batch size in the code

## 🎯 Benefits

1. **Automatic Cleanup**: No manual intervention needed
2. **Real-time Reflection**: Changes appear instantly in Flutter app
3. **Performance**: Keeps database size manageable
4. **Cost Effective**: Reduces Firestore storage costs
5. **Monitoring**: Full visibility into cleanup operations
