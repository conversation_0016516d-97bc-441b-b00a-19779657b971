import 'dart:async';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:money_mouthy_two/controllers/category_controller.dart';
import 'package:money_mouthy_two/services/post_service.dart';

import 'home_app_bar.dart';
import 'home_tab_bar.dart';
import 'explore_tab.dart';
import 'following_tab.dart';
import 'category_data.dart';

/// Home Content Widget - Contains the main home screen logic without bottom navigation
class HomeContent extends StatefulWidget {
  final Function(int)? onCategoryChanged;
  final int? initialCategoryIndex;

  const HomeContent({
    super.key,
    this.onCategoryChanged,
    this.initialCategoryIndex,
  });

  @override
  State<HomeContent> createState() => _HomeContentState();
}

class _HomeContentState extends State<HomeContent>
    with TickerProviderStateMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final CategoryController _categoryController = Get.find<CategoryController>();
  late TabController _tabController;
  StreamSubscription<DocumentSnapshot>? _categorySubscription;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _categorySubscription?.cancel();
    super.dispose();
  }

  Future<void> _initializeServices() async {
    try {
      // WalletManager is already initialized in MainNavigationScreen
      // await WalletManager().initialize();
      await PostService().initialize();
      if (mounted) {
        setState(() {
          // Refresh UI after services are ready
        });
      }
    } catch (e) {
      debugPrint('Error initializing services: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check authentication status
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      // User is not authenticated, show login prompt
      return Scaffold(
        backgroundColor: Colors.white,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.lock_outline,
                size: 64,
                color: Colors.grey,
              ),
              const SizedBox(height: 16),
              const Text(
                'Please log in to access content',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pushReplacementNamed('/login');
                },
                child: const Text('Login'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: Colors.white,
      appBar: HomeAppBar(scaffoldKey: _scaffoldKey),
      body: _buildMainContent(),
    );
  }

  Widget _buildMainContent() {
    return Column(
      children: [
        HomeTabBar(tabController: _tabController),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              Obx(
                () => ExploreTab(
                  key: ValueKey(_categoryController.selectedCategoryIndex),
                  selectedCategory: _categoryController.selectedCategoryName,
                ),
              ),
              const FollowingTab(),
            ],
          ),
        ),
      ],
    );
  }
}
