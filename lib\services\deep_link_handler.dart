import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:app_links/app_links.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/post_service.dart';
import '../screens/post_detail_screen.dart';
import '../screens/login.dart';
import '../screens/main_navigation_screen.dart';

/// Service to handle deep links and navigation
class DeepLinkHandler {
  static final DeepLinkHandler _instance = DeepLinkHandler._internal();
  factory DeepLinkHandler() => _instance;
  DeepLinkHandler._internal();

  final AppLinks _appLinks = AppLinks();
  StreamSubscription<Uri>? _linkSubscription;
  final PostService _postService = PostService();

  // Store pending post ID for after-login navigation
  String? _pendingPostId;

  /// Initialize deep link handling
  Future<void> initialize() async {
    try {
      // Handle web-specific URL parsing
      if (kIsWeb) {
        // For web, we'll handle this differently using route information
        WidgetsBinding.instance.addPostFrameCallback((_) {
          handleWebUrl();
        });
      }

      // Handle initial link when app is launched from a deep link
      final initialLink = await _appLinks.getInitialLink();
      if (initialLink != null) {
        debugPrint('Initial deep link: $initialLink');
        await _handleDeepLink(initialLink);
      }

      // Listen for incoming links when app is already running
      _linkSubscription = _appLinks.uriLinkStream.listen(
        (Uri uri) {
          debugPrint('Incoming deep link: $uri');
          _handleDeepLink(uri);
        },
        onError: (err) {
          debugPrint('Deep link error: $err');
        },
      );
    } catch (e) {
      debugPrint('Failed to initialize deep link handler: $e');
    }
  }

  /// Handle web URL parsing - store the link and process after screens are loaded
  void handleWebUrl() {
    if (!kIsWeb) return;

    try {
      // Get the current URL from browser
      final currentUrl = Uri.base.toString();
      debugPrint('Current URL: $currentUrl');

      // Extract post ID from URL
      final postId = _extractPostIdFromWebUrl(currentUrl);
      if (postId != null && postId.isNotEmpty) {
        debugPrint('Found post ID in URL: $postId');
        // Store the post ID to handle after screens are loaded
        _pendingPostId = postId;
        debugPrint('Stored pending post ID: $postId');
      }
    } catch (e) {
      debugPrint('Error handling web URL: $e');
    }
  }

  /// Extract post ID from web URL (handles hash routing)
  String? _extractPostIdFromWebUrl(String url) {
    try {
      final uri = Uri.parse(url);

      // Check for /#/post/POST_ID format (hash routing)
      if (uri.fragment.isNotEmpty) {
        final fragment = uri.fragment;
        if (fragment.startsWith('/post/')) {
          return fragment.replaceFirst('/post/', '');
        }
      }

      // Check for /post/POST_ID format (direct routing)
      if (uri.pathSegments.length >= 2 && uri.pathSegments[0] == 'post') {
        return uri.pathSegments[1];
      }

      // Check for query parameters
      return uri.queryParameters['postId'] ?? uri.queryParameters['post_id'];
    } catch (e) {
      debugPrint('Error extracting post ID from URL: $e');
      return null;
    }
  }

  /// Handle incoming deep link
  Future<void> _handleDeepLink(Uri uri) async {
    try {
      debugPrint('Processing deep link: $uri');

      // Parse the URI to extract post information
      final linkData = _parseDeepLink(uri);

      if (linkData == null) {
        debugPrint('Invalid deep link format: $uri');
        _navigateToHome();
        return;
      }

      // Check if user is authenticated
      final user = FirebaseAuth.instance.currentUser;

      if (user == null) {
        // Email verification check removed
        // Store post ID for after-login navigation
        _pendingPostId = linkData.postId;
        debugPrint('User not authenticated, storing post ID: $_pendingPostId');
        _navigateToLogin();
        return;
      }

      // User is authenticated, navigate to post
      await _navigateToPost(linkData.postId);
    } catch (e) {
      debugPrint('Error handling deep link: $e');
      _navigateToHome();
    }
  }

  /// Parse deep link URI and extract post information
  DeepLinkData? _parseDeepLink(Uri uri) {
    try {
      // Handle different URL formats:
      // moneymouthy://post/POST_ID
      // https://moneymouthy.com/post/POST_ID
      // http://moneymouthy.com/post/POST_ID

      final pathSegments = uri.pathSegments;

      // Check if it's a post link
      if (pathSegments.length >= 2 && pathSegments[0] == 'post') {
        final postId = pathSegments[1];
        if (postId.isNotEmpty) {
          return DeepLinkData(
            type: DeepLinkType.post,
            postId: postId,
          );
        }
      }

      // Check for query parameters as fallback
      final postId =
          uri.queryParameters['postId'] ?? uri.queryParameters['post_id'];
      if (postId != null && postId.isNotEmpty) {
        return DeepLinkData(
          type: DeepLinkType.post,
          postId: postId,
        );
      }

      return null;
    } catch (e) {
      debugPrint('Error parsing deep link: $e');
      return null;
    }
  }

  /// Navigate to post detail screen after screens are loaded
  Future<void> _navigateToPost(String postId) async {
    try {
      debugPrint('Navigating to post: $postId');

      // Ensure PostService is initialized
      await _postService.initialize();

      // Find the post
      final posts = _postService.getAllPosts();
      final post = posts.firstWhereOrNull((p) => p.id == postId);

      if (post != null) {
        debugPrint('Post found, navigating to detail screen');
        // Navigate to post detail screen
        Get.to(
          () => PostDetailScreen(post: post),
          transition: Transition.cupertino,
          duration: const Duration(milliseconds: 300),
        );
      } else {
        debugPrint('Post not found in cache: $postId');
        _showPostNotFoundDialog();
      }
    } catch (e) {
      debugPrint('Error navigating to post: $e');
      _showPostNotFoundDialog();
    }
  }

  /// Handle pending navigation after screens are loaded
  Future<void> handlePendingNavigation() async {
    if (_pendingPostId != null) {
      final postId = _pendingPostId!;
      _pendingPostId = null; // Clear pending post ID

      debugPrint('Handling pending navigation to post: $postId');

      // Small delay to ensure screens are fully loaded
      await Future.delayed(const Duration(milliseconds: 500));

      await _navigateToPost(postId);
    }
  }

  /// Navigate to login screen
  void _navigateToLogin() {
    if (kIsWeb) {
      Get.offAllNamed('/login');
    } else {
      Get.offAll(
        () => const LoginScreen(),
        transition: Transition.cupertino,
        duration: const Duration(milliseconds: 300),
      );
    }
  }

  /// Navigate to home screen
  void _navigateToHome() {
    if (kIsWeb) {
      Get.offAllNamed('/home');
    } else {
      Get.offAll(
        () => const MainNavigationScreen(),
        transition: Transition.cupertino,
        duration: const Duration(milliseconds: 300),
      );
    }
  }

  /// Show post not found dialog
  void _showPostNotFoundDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Post Not Found'),
        content: const Text(
          'The post you\'re looking for could not be found. It may have been deleted or is no longer available.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back(); // Close dialog
              _navigateToHome();
            },
            child: const Text('Go to Home'),
          ),
        ],
      ),
    );
  }

  /// Handle post-login navigation if there's a pending post
  Future<void> handlePostLoginNavigation() async {
    if (_pendingPostId != null) {
      final postId = _pendingPostId!;
      _pendingPostId = null; // Clear pending post ID

      debugPrint('Handling post-login navigation to post: $postId');

      // Small delay to ensure the main screen is loaded
      await Future.delayed(const Duration(milliseconds: 500));

      await _navigateToPost(postId);
    }
  }

  /// Check if there's a pending post navigation
  bool get hasPendingNavigation => _pendingPostId != null;

  /// Get pending post ID
  String? get pendingPostId => _pendingPostId;

  /// Clear pending navigation
  void clearPendingNavigation() {
    _pendingPostId = null;
  }

  /// Generate shareable URL for a post
  String generatePostUrl(String postId, {bool useCustomScheme = false}) {
    if (useCustomScheme) {
      return 'moneymouthy://post/$postId';
    } else {
      // Use web URL for better compatibility
      return 'https://moneymouthy.com/post/$postId';
    }
  }

  /// Dispose resources
  void dispose() {
    _linkSubscription?.cancel();
    _linkSubscription = null;
  }
}

/// Data class for parsed deep link information
class DeepLinkData {
  final DeepLinkType type;
  final String postId;

  const DeepLinkData({
    required this.type,
    required this.postId,
  });
}

/// Types of deep links supported
enum DeepLinkType {
  post,
}
