import 'package:flutter/material.dart';
import '../models/user_management_model.dart';

class ActivityTypeChip extends StatelessWidget {
  final UserActivityType type;

  const ActivityTypeChip({
    super.key,
    required this.type,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isMobile ? 4 : 8,
        vertical: isMobile ? 2 : 4,
      ),
      decoration: BoxDecoration(
        color: _getTypeColor().withOpacity(0.1),
        borderRadius: BorderRadius.circular(isMobile ? 8 : 12),
        border: Border.all(color: _getTypeColor().withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getTypeIcon(),
            color: _getTypeColor(),
            size: isMobile ? 10 : 12,
          ),
          SizedBox(width: isMobile ? 2 : 4),
          Text(
            _getTypeText(),
            style: TextStyle(
              color: _getTypeColor(),
              fontSize: isMobile ? 8 : 10,
              fontWeight: FontWeight.w600,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Color _getTypeColor() {
    switch (type) {
      case UserActivityType.login:
        return Colors.green;
      case UserActivityType.logout:
        return Colors.orange;
      case UserActivityType.postCreated:
        return Colors.blue;
      case UserActivityType.postDeleted:
        return Colors.red;
      case UserActivityType.walletTransaction:
        return Colors.purple;
      case UserActivityType.profileUpdate:
        return Colors.teal;
      case UserActivityType.passwordChange:
        return Colors.indigo;
    }
  }

  IconData _getTypeIcon() {
    switch (type) {
      case UserActivityType.login:
        return Icons.login;
      case UserActivityType.logout:
        return Icons.logout;
      case UserActivityType.postCreated:
        return Icons.post_add;
      case UserActivityType.postDeleted:
        return Icons.delete;
      case UserActivityType.walletTransaction:
        return Icons.account_balance_wallet;
      case UserActivityType.profileUpdate:
        return Icons.edit;
      case UserActivityType.passwordChange:
        return Icons.lock;
    }
  }

  String _getTypeText() {
    switch (type) {
      case UserActivityType.login:
        return 'LOGIN';
      case UserActivityType.logout:
        return 'LOGOUT';
      case UserActivityType.postCreated:
        return 'POST';
      case UserActivityType.postDeleted:
        return 'DELETE';
      case UserActivityType.walletTransaction:
        return 'WALLET';
      case UserActivityType.profileUpdate:
        return 'PROFILE';
      case UserActivityType.passwordChange:
        return 'PASSWORD';
    }
  }
}
