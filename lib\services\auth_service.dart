import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../admin/models/admin_user_model.dart';

class AuthService extends GetxController {
  static AuthService get to => Get.find();
  
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  // Reactive variables
  final _currentUser = Rxn<User>();
  final _isAdmin = false.obs;
  final _adminUser = Rxn<AdminUserModel>();
  final _isLoading = false.obs;
  
  // Getters
  User? get currentUser => _currentUser.value;
  bool get isAdmin => _isAdmin.value;
  AdminUserModel? get adminUser => _adminUser.value;
  bool get isLoading => _isLoading.value;
  bool get isAuthenticated => _currentUser.value != null;
  
  @override
  void onInit() {
    super.onInit();
    _initializeAuthState();
  }
  
  void _initializeAuthState() {
    // Listen to Firebase auth state changes
    _auth.authStateChanges().listen((User? user) async {
      _currentUser.value = user;
      
      if (user != null) {
        await _checkIfUserIsAdmin(user.uid);
      } else {
        _isAdmin.value = false;
        _adminUser.value = null;
      }
    });
  }
  
  Future<void> _checkIfUserIsAdmin(String userId) async {
    try {
      _isLoading.value = true;
      
      // Check if user exists in admins collection
      final adminDoc = await _firestore
          .collection('admins')
          .doc(userId)
          .get();
      
      if (adminDoc.exists) {
        final adminData = AdminUserModel.fromMap(userId, adminDoc.data()!);
        
        // Only set as admin if status is active
        if (adminData.status == AdminStatus.active) {
          _isAdmin.value = true;
          _adminUser.value = adminData;
          
          // Update last login for admin
          await _updateAdminLastLogin(userId);
        } else {
          _isAdmin.value = false;
          _adminUser.value = null;
        }
      } else {
        _isAdmin.value = false;
        _adminUser.value = null;
      }
    } catch (e) {
      debugPrint('Error checking admin status: $e');
      _isAdmin.value = false;
      _adminUser.value = null;
    } finally {
      _isLoading.value = false;
    }
  }
  
  Future<void> _updateAdminLastLogin(String adminId) async {
    try {
      await _firestore
          .collection('admins')
          .doc(adminId)
          .update({
        'lastLogin': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error updating admin last login: $e');
    }
  }
  
  /// Sign in with email and password
  Future<AuthResult> signIn(String email, String password) async {
    try {
      _isLoading.value = true;
      
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      if (credential.user != null) {
        // Wait for admin check to complete
        await _checkIfUserIsAdmin(credential.user!.uid);
        
        return AuthResult(
          success: true,
          user: credential.user!,
          isAdmin: _isAdmin.value,
          adminUser: _adminUser.value,
        );
      }
      
      return AuthResult(
        success: false,
        errorMessage: 'Authentication failed',
      );
    } on FirebaseAuthException catch (e) {
      return AuthResult(
        success: false,
        errorMessage: _getAuthErrorMessage(e.code),
      );
    } catch (e) {
      debugPrint('Sign in error: $e');
      return AuthResult(
        success: false,
        errorMessage: 'An unexpected error occurred',
      );
    } finally {
      _isLoading.value = false;
    }
  }
  
  /// Sign out
  Future<void> signOut() async {
    try {
      await _auth.signOut();
      _currentUser.value = null;
      _isAdmin.value = false;
      _adminUser.value = null;
    } catch (e) {
      debugPrint('Error signing out: $e');
    }
  }
  
  /// Check if current user is admin (synchronous)
  bool get isCurrentUserAdmin => _isAdmin.value;
  
  /// Get admin permissions
  List<String> get adminPermissions => _adminUser.value?.permissions ?? [];
  
  /// Check if admin has specific permission
  bool hasAdminPermission(String permission) {
    return _adminUser.value?.hasPermission(permission) ?? false;
  }
  
  String _getAuthErrorMessage(String code) {
    switch (code) {
      case 'user-not-found':
        return 'No user found with this email address';
      case 'wrong-password':
        return 'Incorrect password';
      case 'invalid-email':
        return 'Invalid email address';
      case 'user-disabled':
        return 'This account has been disabled';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later';
      case 'network-request-failed':
        return 'Network error. Please check your connection';
      default:
        return 'Authentication failed. Please try again';
    }
  }
}

/// Result class for authentication operations
class AuthResult {
  final bool success;
  final User? user;
  final bool isAdmin;
  final AdminUserModel? adminUser;
  final String? errorMessage;
  
  const AuthResult({
    required this.success,
    this.user,
    this.isAdmin = false,
    this.adminUser,
    this.errorMessage,
  });
}
