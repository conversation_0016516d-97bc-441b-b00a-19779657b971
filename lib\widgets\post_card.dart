import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:image_network/image_network.dart';
import 'package:get/get.dart';
import '../controllers/profile_controller.dart';
import '../services/post_service.dart';
import '../screens/media_player_screen.dart';
import '../screens/image_gallery_screen.dart';
import '../services/video_thumbnail_service.dart';
import '../services/profile_update_service.dart';
import '../services/user_cache_service.dart';
import '../services/post_sharing_service.dart';
import '../utils/smooth_page_route.dart';
import 'poll_widget.dart';

class PostCard extends StatefulWidget {
  final Post post;
  final bool isTablet;
  final VoidCallback? onLike;
  final VoidCallback? onView;
  final VoidCallback? onTap;
  final bool isDetailView;
  // Whether to show the actions section (like, views)
  final bool? showActions;

  const PostCard({
    super.key,
    required this.post,
    this.isTablet = false,
    this.onLike,
    this.onView,
    this.onTap,
    this.isDetailView = false,
    this.showActions = true,
  });

  @override
  State<PostCard> createState() => _PostCardState();
}

class _PostCardState extends State<PostCard>
    with AutomaticKeepAliveClientMixin {
  final ProfileUpdateService _profileUpdateService = ProfileUpdateService();
  final ProfileController _profileController = Get.find<ProfileController>();
  final UserCacheService _userCacheService = Get.find<UserCacheService>();
  final PostSharingService _sharingService = PostSharingService();
  String? _userProfileImage;
  Map<String, dynamic>? _userData;
  bool _isLiked = false;
  StreamSubscription<Map<String, dynamic>>? _profileUpdateSubscription;

  // Cache to prevent unnecessary data loading
  bool _isDataLoaded = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _loadUserData();
    _checkLikeStatus();
    // Track view when post is displayed
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.onView?.call();
    });

    // Listen to profile updates for real-time changes
    _profileUpdateSubscription =
        _profileUpdateService.profileDataUpdateStream.listen((updateData) {
      final userId = updateData['userId'] as String?;
      if (mounted && userId == widget.post.authorId) {
        // Refresh user data when the post author's profile is updated
        _loadUserData();
      }
    });
  }

  @override
  void dispose() {
    _profileUpdateSubscription?.cancel();
    super.dispose();
  }

  void _checkLikeStatus() {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser != null) {
      setState(() {
        _isLiked = widget.post.likedBy.contains(currentUser.uid);
      });
    }
  }

  @override
  void didUpdateWidget(PostCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Update like status when post data changes
    if (oldWidget.post.likedBy != widget.post.likedBy) {
      _checkLikeStatus();
    }
  }

  Future<void> _loadUserData() async {
    // Prevent loading data multiple times
    if (_isDataLoaded) return;

    try {
      // Check if this is the current user's post
      final currentUserId = _profileController.currentUserId;

      if (widget.post.authorId == currentUserId && currentUserId.isNotEmpty) {
        // Use ProfileController data for current user - reactive approach
        if (mounted) {
          setState(() {
            _userData = {
              'name': _profileController.displayName,
              'username': _profileController.username,
              'bio': _profileController.bio,
              'profileImageUrl': _profileController.profileImageUrl,
            };
            _userProfileImage = _profileController.profileImageUrl.isNotEmpty
                ? _profileController.profileImageUrl
                : null;
            _isDataLoaded = true;
          });
        }
      } else {
        // Use UserCacheService for other users (optimized caching)
        final userData = await _userCacheService.getUserData(
          widget.post.authorId,
        );
        if (mounted && userData != null) {
          final profileImageUrl =
              userData['profileImageUrl'] ?? userData['photoUrl'];
          setState(() {
            _userData = userData;
            _userProfileImage = profileImageUrl;
            _isDataLoaded = true;
          });
        }
      }
    } catch (e) {
      debugPrint('PostCard: Error loading user data: $e');
      // Handle error silently, will use default data
    }
  }

  String _getPreviewContent(String content) {
    if (content.isEmpty) return '';

    const int maxLength = 120;
    if (content.length <= maxLength) {
      return content;
    }

    return '${content.substring(0, maxLength)}...';
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    final bool isPaidPost = widget.post.price > 0;
    final bool hasAccess = widget.post.isPaid || !isPaidPost;
    final bool showActions = widget.showActions ?? !widget.isDetailView;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeInOut,
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOutCubic,
          margin: EdgeInsets.only(bottom: 4),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
              BoxShadow(
                color: Colors.black.withOpacity(0.02),
                blurRadius: 16,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header - Facebook-style user info
              _buildUserHeader(isPaidPost, hasAccess),
// Media content (images, videos, links)
              _buildMediaContent(),
              // Post content
              _buildPostContent(hasAccess),
              // Poll content (if post has a poll)
              if (widget.post.hasPoll)
                PollWidget(
                  post: widget.post,
                  isDetailView: widget.isDetailView,
                ),

              if (showActions) ...[
                // Interaction section
                _buildInteractionSection(isPaidPost, hasAccess),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUserAvatar(String? profileImageUrl) {
    return profileImageUrl != null && profileImageUrl.isNotEmpty
        ? CircleAvatar(
            radius: widget.isTablet ? 24 : 20,
            backgroundColor: const Color(0xFF5159FF),
            child: ClipOval(
              child: ImageNetwork(
                image: profileImageUrl,
                height: widget.isTablet ? 48 : 40,
                width: widget.isTablet ? 48 : 40,
                duration: 100,
                curve: Curves.easeIn,
                onPointer: true,
                debugPrint: false,
                backgroundColor: Colors.white,
                fitAndroidIos: BoxFit.cover,
                fitWeb: BoxFitWeb.cover,
                borderRadius: BorderRadius.circular(70),
                onLoading: const CircularProgressIndicator(
                  color: Colors.indigoAccent,
                  strokeWidth: 0.1,
                ),
                onError: Text(
                  widget.post.author.split(' ').map((e) => e[0]).take(2).join(),
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: widget.isTablet ? 16 : 14,
                  ),
                ),
              ),
            ),
          )
        : CircleAvatar(
            radius: widget.isTablet ? 24 : 20,
            backgroundColor: const Color(0xFF5159FF),
            child: Text(
              widget.post.author.split(' ').map((e) => e[0]).take(2).join(),
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: widget.isTablet ? 16 : 14,
              ),
            ),
          );
  }

  Widget _buildUserHeader(bool isPaidPost, bool hasAccess) {
    final isCurrentUser =
        widget.post.authorId == _profileController.currentUserId;

    return Padding(
      padding: EdgeInsets.all(8),
      child: Row(
        children: [
          // User avatar - reactive for current user
          isCurrentUser
              ? _buildUserAvatar(_profileController.profileImageUrl)
              : _buildUserAvatar(_userProfileImage),
          SizedBox(width: widget.isTablet ? 12 : 10),

          // User info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Display name and paid badge row
                Row(
                  children: [
                    Expanded(
                      child: isCurrentUser
                          ? Obx(
                              () => Text(
                                _profileController.displayName.isNotEmpty
                                    ? _profileController.displayName
                                    : widget.post.author,
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: widget.isTablet ? 16 : 14,
                                  color: Colors.black87,
                                ),
                              ),
                            )
                          : Text(
                              _userData?['name'] ?? widget.post.author,
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: widget.isTablet ? 16 : 14,
                                color: Colors.black87,
                              ),
                            ),
                    ),
                    if (isPaidPost) ...[
                      const SizedBox(width: 6),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: hasAccess ? Colors.green : Colors.grey[500]!,
                            width: 0.5,
                          ),
                          // color: hasAccess ? Colors.green.withOpacity(0.1) : const Color(0xFF5159FF).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          hasAccess ? 'OWNED' : 'PAID',
                          style: TextStyle(
                            color: hasAccess
                                ? Colors.green
                                : const Color(0xFF5159FF),
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                // Username in gray
                isCurrentUser
                    ? Obx(
                        () => _profileController.username.isNotEmpty
                            ? Text(
                                '@${_profileController.username}',
                                style: TextStyle(
                                  fontSize: widget.isTablet ? 13 : 12,
                                  color: Colors.grey[600],
                                ),
                              )
                            : const SizedBox.shrink(),
                      )
                    : (_userData?['username'] != null
                        ? Text(
                            '@${_userData!['username']}',
                            style: TextStyle(
                              fontSize: widget.isTablet ? 13 : 12,
                              color: Colors.grey[600],
                            ),
                          )
                        : const SizedBox.shrink()),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Text(
                      widget.post.timeAgo,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: widget.isTablet ? 13 : 12,
                      ),
                    ),
                    if (widget.post.category.isNotEmpty) ...[
                      Text(
                        ' • ',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: widget.isTablet ? 13 : 12,
                        ),
                      ),
                      Text(
                        widget.post.category,
                        style: TextStyle(
                          color: const Color(0xFF5159FF),
                          fontSize: widget.isTablet ? 13 : 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),

          // // More options button
          // IconButton(
          //   onPressed: () => _showPostOptions(context),
          //   icon: Icon(
          //     Icons.more_horiz,
          //     color: Colors.grey[600],
          //     size: widget.isTablet ? 24 : 20,
          //   ),
          //   padding: EdgeInsets.zero,
          //   constraints: const BoxConstraints(),
          // ),
        ],
      ),
    );
  }

  Widget _buildPostContent(bool hasAccess) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title (if exists)
        if (widget.post.title != null && widget.post.title!.isNotEmpty)
          Padding(
            padding: EdgeInsets.fromLTRB(
              8,
              8,
              8,
              0,
            ),
            child: Text(
              widget.post.title!,
              style: TextStyle(
                fontSize: widget.isTablet ? 20 : 17,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
                height: 1.3,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),

        // Content text
        Padding(
          padding: EdgeInsets.fromLTRB(
            8,
            0,
            8,
            8,
          ),
          child: Text(
            hasAccess
                ? widget.post.content
                : _getPreviewContent(widget.post.content),
            style: TextStyle(
              fontSize: 14,
              color: hasAccess ? Colors.black87 : Colors.grey[700],
              height: 1.4,
            ),
            maxLines: hasAccess ? null : 4,
            overflow: hasAccess ? null : TextOverflow.ellipsis,
          ),
        ),

        // Tags
        if (widget.post.tags.isNotEmpty)
          Padding(
            padding: EdgeInsets.fromLTRB(
              8,
              0,
              8,
              8,
            ),
            child: Wrap(
              spacing: 6,
              runSpacing: 6,
              children: widget.post.tags.map((tag) {
                return Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    tag,
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontSize: widget.isTablet ? 12 : 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
      ],
    );
  }

  Widget _buildMediaContent() {
    final hasImages = widget.post.imageUrls.isNotEmpty;
    final hasVideos = widget.post.videoUrls.isNotEmpty;
    final hasLink =
        widget.post.linkUrl != null && widget.post.linkUrl!.isNotEmpty;

    // Check if we have multiple media items (images + videos)
    final totalMediaCount =
        widget.post.imageUrls.length + widget.post.videoUrls.length;
    final hasMultipleMedia = totalMediaCount > 1;

    if (hasMultipleMedia) {
      // Use horizontal scrollable layout for multiple media
      return _buildHorizontalMediaContent();
    } else {
      // Use original layout for single media items
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Images gallery
          if (hasImages) _buildImageGallery(),

          // Videos
          if (hasVideos) ...[
            if (hasImages) const SizedBox(height: 8),
            _buildVideoSection(),
          ],

          // URL Link preview
          if (hasLink) ...[
            if (hasImages || hasVideos) const SizedBox(height: 8),
            _buildLinkPreview(),
          ],
        ],
      );
    }
  }

  Widget _buildHorizontalMediaContent() {
    final hasImages = widget.post.imageUrls.isNotEmpty;
    final hasVideos = widget.post.videoUrls.isNotEmpty;
    final hasLink =
        widget.post.linkUrl != null && widget.post.linkUrl!.isNotEmpty;

    // Create a combined list of media items (videos first, then images)
    List<Widget> mediaItems = [];

    // Add videos first
    if (hasVideos) {
      for (int i = 0; i < widget.post.videoUrls.length; i++) {
        mediaItems.add(_buildVideoItem(widget.post.videoUrls[i], i));
      }
    }

    // Add images after videos
    if (hasImages) {
      for (int i = 0; i < widget.post.imageUrls.length; i++) {
        mediaItems.add(_buildImageItem(widget.post.imageUrls[i], i));
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Horizontal scrollable media
        if (mediaItems.isNotEmpty) ...[
          SizedBox(
            height: 160, // Fixed height for media area
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: EdgeInsets.symmetric(
                horizontal: 8,
              ),
              itemCount: mediaItems.length,
              itemBuilder: (context, index) {
                return Container(
                  width: 200, // Fixed width for each media item
                  margin: EdgeInsets.only(
                    right: index < mediaItems.length - 1 ? 8 : 0,
                  ),
                  child: mediaItems[index],
                );
              },
            ),
          ),
        ],

        // Link preview (if exists)
        if (hasLink) ...[
          SizedBox(height: widget.isTablet ? 12 : 8),
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: 8,
            ),
            child: _buildLinkPreview(),
          ),
        ],
      ],
    );
  }

  Widget _buildVideoItem(String videoUrl, int index) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: () => _openMediaPlayer(initialIndex: index),
        child: Container(
          height: 160,
          color: Colors.black,
          child: Stack(
            children: [
              // Video thumbnail
              Positioned.fill(child: _buildVideoThumbnailForUrl(videoUrl)),

              // Play button overlay
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.play_circle_filled,
                      color: Colors.white,
                      size: 40,
                    ),
                  ),
                ),
              ),

              // Video badge (top left)
              Positioned(
                top: 8,
                left: 8,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'VIDEO',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

              // Video duration badge (bottom right)
              Positioned(
                bottom: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 4,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    _getVideoDuration(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageItem(String imageUrl, int index) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: () => _openImageGallery(index),
        child: SizedBox(
          height: 160,
          child: kIsWeb
              ? ImageNetwork(
                  borderRadius: BorderRadius.circular(12),
                  image: imageUrl,
                  height: 160,
                  width: 300,
                  duration: 500,
                  curve: Curves.easeIn,
                  onPointer: false,
                  debugPrint: false,
                  backgroundColor: Colors.grey[100]!,
                  fitAndroidIos: BoxFit.cover,
                  fitWeb: BoxFitWeb.cover,
                  onLoading: const CircularProgressIndicator(
                    color: Colors.indigoAccent,
                    strokeWidth: 1.0,
                  ),
                  onError: Container(
                    height: 160,
                    color: Colors.grey[300],
                    child: const Icon(
                      Icons.image_not_supported,
                      color: Colors.grey,
                      size: 40,
                    ),
                  ),
                )
              : Image.network(
                  imageUrl,
                  height: 160,
                  width: 300,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      height: 160,
                      color: Colors.grey[300],
                      child: const Icon(
                        Icons.image_not_supported,
                        color: Colors.grey,
                        size: 40,
                      ),
                    );
                  },
                ),
        ),
      ),
    );
  }

  Widget _buildVideoThumbnailForUrl(String videoUrl) {
    if (kIsWeb) {
      // For web, try to show the video element with poster frame
      return _buildWebVideoThumbnail(videoUrl);
    }

    return FutureBuilder<String?>(
      future: VideoThumbnailService.generateThumbnailFromUrl(videoUrl),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return _buildLoadingThumbnail();
        }

        if (snapshot.hasData && snapshot.data != null) {
          return Stack(
            children: [
              // Actual video thumbnail
              Positioned.fill(
                child: Image.file(
                  File(snapshot.data!),
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildPlaceholderThumbnail();
                  },
                ),
              ),
              // Video quality badge
              Positioned(
                top: 8,
                left: 8,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'HD',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          );
        }

        return _buildPlaceholderThumbnail();
      },
    );
  }

  Widget _buildWebVideoThumbnail(String videoUrl) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF1a1a1a),
            const Color(0xFF2d2d2d),
            const Color(0xFF1a1a1a),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Try to show video poster/first frame
          Positioned.fill(
            child: Container(
              color: Colors.black87,
              child: const Center(
                child: Icon(Icons.videocam, size: 48, color: Colors.white54),
              ),
            ),
          ),
          // Video icon overlay
          Center(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.play_circle_filled,
                size: 40,
                color: Colors.white.withOpacity(0.9),
              ),
            ),
          ),
          // Video quality badge
          Positioned(
            top: 8,
            left: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Text(
                'HD',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageGallery() {
    final images = widget.post.imageUrls;

    if (images.isEmpty) return const SizedBox.shrink();

    return GestureDetector(
      onTap: () =>
          _openImageGallery(0), // Open gallery starting from first image
      child: Container(
        margin: EdgeInsets.symmetric(
          horizontal: widget.isTablet ? 16 : 12,
          vertical: 8,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: _buildImageLayout(images),
        ),
      ),
    );
  }

  Widget _buildImageLayout(List<String> images) {
    if (images.length == 1) {
      return AspectRatio(
        aspectRatio: 16 / 9,
        child: AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          child: kIsWeb
              ? ImageNetwork(
                  image: images[0],
                  borderRadius: BorderRadius.circular(12),
                  height: 200,
                  width: 400,
                  duration: 500,
                  curve: Curves.easeIn,
                  onPointer: false,
                  debugPrint: false,
                  backgroundColor: Colors.grey[100]!,
                  fitAndroidIos: BoxFit.cover,
                  fitWeb: BoxFitWeb.cover,
                  onLoading: const CircularProgressIndicator(
                    color: Colors.indigoAccent,
                    strokeWidth: 0.1,
                  ),
                  onError: AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    color: Colors.grey[200],
                    child: const Icon(
                      Icons.image_not_supported,
                      color: Colors.grey,
                    ),
                  ),
                )
              : Image.network(
                  images[0],
                  fit: BoxFit.cover,
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) {
                      return FadeTransition(
                        opacity: const AlwaysStoppedAnimation(1.0),
                        child: child,
                      );
                    }
                    return Container(
                      color: Colors.grey[100],
                      child: Center(
                        child: CircularProgressIndicator(
                          value: loadingProgress.expectedTotalBytes != null
                              ? loadingProgress.cumulativeBytesLoaded /
                                  loadingProgress.expectedTotalBytes!
                              : null,
                          strokeWidth: 2,
                          valueColor: const AlwaysStoppedAnimation<Color>(
                            Color(0xFF4285F4),
                          ),
                        ),
                      ),
                    );
                  },
                  errorBuilder: (context, error, stackTrace) =>
                      AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    color: Colors.grey[200],
                    child: const Icon(
                      Icons.image_not_supported,
                      color: Colors.grey,
                    ),
                  ),
                ),
        ),
      );
    } else if (images.length == 2) {
      return SizedBox(
        height: 200,
        child: Row(
          children: images.asMap().entries.map((entry) {
            final index = entry.key;
            final url = entry.value;
            return Expanded(
              child: GestureDetector(
                onTap: () => _openImageGallery(index),
                child: Container(
                  margin: const EdgeInsets.only(right: 2),
                  child: kIsWeb
                      ? ImageNetwork(
                          image: url,
                          borderRadius: BorderRadius.circular(12),
                          height: 200,
                          width: 400,
                          duration: 500,
                          curve: Curves.easeIn,
                          onPointer: false,
                          debugPrint: false,
                          backgroundColor: Colors.grey[100]!,
                          fitAndroidIos: BoxFit.cover,
                          fitWeb: BoxFitWeb.cover,
                          onLoading: const CircularProgressIndicator(
                            color: Colors.indigoAccent,
                            strokeWidth: 0.1,
                          ),
                          onError: Container(
                            color: Colors.grey[200],
                            child: const Icon(
                              Icons.image_not_supported,
                              color: Colors.grey,
                            ),
                          ),
                        )
                      : Image.network(
                          url,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              Container(
                            color: Colors.grey[200],
                            child: const Icon(
                              Icons.image_not_supported,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                ),
              ),
            );
          }).toList(),
        ),
      );
    } else {
      // 3+ images: show first 2 large, then grid for rest
      return SizedBox(
        height: 200,
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: GestureDetector(
                onTap: () => _openImageGallery(0),
                child: kIsWeb
                    ? ImageNetwork(
                        image: images[0],
                        borderRadius: BorderRadius.circular(12),
                        height: 200,
                        width: 400,
                        duration: 500,
                        curve: Curves.easeIn,
                        onPointer: false,
                        debugPrint: false,
                        backgroundColor: Colors.grey[100]!,
                        fitAndroidIos: BoxFit.cover,
                        fitWeb: BoxFitWeb.cover,
                        onLoading: const CircularProgressIndicator(
                          color: Colors.indigoAccent,
                          strokeWidth: 0.1,
                        ),
                        onError: Container(
                          color: Colors.grey[200],
                          child: const Icon(
                            Icons.image_not_supported,
                            color: Colors.grey,
                          ),
                        ),
                      )
                    : Image.network(
                        images[0],
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => Container(
                          color: Colors.grey[200],
                          child: const Icon(
                            Icons.image_not_supported,
                            color: Colors.grey,
                          ),
                        ),
                      ),
              ),
            ),
            const SizedBox(width: 2),
            Expanded(
              child: Column(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () => _openImageGallery(1),
                      child: kIsWeb
                          ? ImageNetwork(
                              image: images[1],
                              borderRadius: BorderRadius.circular(12),
                              height: 100,
                              width: 200,
                              duration: 500,
                              curve: Curves.easeIn,
                              onPointer: false,
                              debugPrint: false,
                              backgroundColor: Colors.grey[100]!,
                              fitAndroidIos: BoxFit.cover,
                              fitWeb: BoxFitWeb.cover,
                              onLoading: const CircularProgressIndicator(
                                color: Colors.indigoAccent,
                                strokeWidth: 0.1,
                              ),
                              onError: Container(
                                color: Colors.grey[200],
                                child: const Icon(
                                  Icons.image_not_supported,
                                  color: Colors.grey,
                                ),
                              ),
                            )
                          : Image.network(
                              images[1],
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  Container(
                                color: Colors.grey[200],
                                child: const Icon(
                                  Icons.image_not_supported,
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                    ),
                  ),
                  if (images.length > 2) ...[
                    const SizedBox(height: 2),
                    Expanded(
                      child: GestureDetector(
                        onTap: () => _openImageGallery(2),
                        child: Stack(
                          children: [
                            kIsWeb
                                ? ImageNetwork(
                                    image: images[2],
                                    borderRadius: BorderRadius.circular(12),
                                    height: 100,
                                    width: 200,
                                    duration: 500,
                                    curve: Curves.easeIn,
                                    onPointer: false,
                                    debugPrint: false,
                                    backgroundColor: Colors.grey[100]!,
                                    fitAndroidIos: BoxFit.cover,
                                    fitWeb: BoxFitWeb.cover,
                                    onLoading: const CircularProgressIndicator(
                                      color: Colors.indigoAccent,
                                      strokeWidth: 0.1,
                                    ),
                                    onError: Container(
                                      color: Colors.grey[200],
                                      child: const Icon(
                                        Icons.image_not_supported,
                                        color: Colors.grey,
                                      ),
                                    ),
                                  )
                                : Image.network(
                                    images[2],
                                    fit: BoxFit.cover,
                                    width: double.infinity,
                                    errorBuilder:
                                        (context, error, stackTrace) =>
                                            Container(
                                      color: Colors.grey[200],
                                      child: const Icon(
                                        Icons.image_not_supported,
                                        color: Colors.grey,
                                      ),
                                    ),
                                  ),
                            if (images.length > 3)
                              Container(
                                color: Colors.black.withOpacity(0.6),
                                child: Center(
                                  child: Text(
                                    '+${images.length - 3}',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildVideoSection() {
    return GestureDetector(
      onTap: () => _openMediaPlayer(),
      child: Container(
        margin: EdgeInsets.symmetric(
          horizontal: widget.isTablet ? 16 : 12,
          vertical: 8,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: AspectRatio(
            aspectRatio: 16 / 9,
            child: Container(
              color: Colors.black,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Video thumbnail or placeholder
                  _buildVideoThumbnail(),

                  // Play button overlay
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.3),
                      shape: BoxShape.circle,
                    ),
                    padding: const EdgeInsets.all(16),
                    child: const Icon(
                      Icons.play_arrow,
                      size: 48,
                      color: Colors.white,
                    ),
                  ),

                  // Video count badge
                  Positioned(
                    bottom: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.8),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.videocam,
                            size: 14,
                            color: Colors.white,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${widget.post.videoUrls.length}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Duration badge with better styling
                  Positioned(
                    bottom: 8,
                    left: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.8),
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.2),
                          width: 0.5,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.access_time,
                            size: 10,
                            color: Colors.white.withOpacity(0.8),
                          ),
                          const SizedBox(width: 2),
                          Text(
                            _getVideoDuration(), // Dynamic duration
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 11,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildVideoThumbnail() {
    if (widget.post.videoUrls.isEmpty) {
      return _buildPlaceholderThumbnail();
    }

    return FutureBuilder<String?>(
      future: VideoThumbnailService.generateThumbnailFromUrl(
        widget.post.videoUrls.first,
      ),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return _buildLoadingThumbnail();
        }

        if (snapshot.hasData && snapshot.data != null) {
          return Stack(
            children: [
              // Actual video thumbnail
              Positioned.fill(
                child: Image.file(
                  File(snapshot.data!),
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildPlaceholderThumbnail();
                  },
                ),
              ),
              // Video quality badge
              Positioned(
                top: 8,
                left: 8,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'HD',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          );
        }

        return _buildPlaceholderThumbnail();
      },
    );
  }

  Widget _buildPlaceholderThumbnail() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF1a1a1a),
            const Color(0xFF2d2d2d),
            const Color(0xFF1a1a1a),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Background pattern
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  center: Alignment.center,
                  radius: 1.0,
                  colors: [
                    Colors.white.withOpacity(0.05),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
          // Video icon
          Center(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.play_circle_filled,
                size: 40,
                color: Colors.white.withOpacity(0.9),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingThumbnail() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.grey[300],
      child: const Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
        ),
      ),
    );
  }

  Widget _buildLinkPreview() {
    return GestureDetector(
      onTap: () => _launchUrl(widget.post.linkUrl!),
      child: Container(
        margin: EdgeInsets.symmetric(
          horizontal: widget.isTablet ? 16 : 12,
          vertical: 8,
        ),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 120,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
              ),
              child: const Center(
                child: Icon(Icons.link, size: 32, color: Colors.grey),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.post.linkUrl!,
                    style: TextStyle(
                      fontSize: widget.isTablet ? 14 : 13,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF5159FF),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Tap to open externally',
                    style: TextStyle(
                      fontSize: widget.isTablet ? 12 : 11,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _launchUrl(String url) async {
    try {
      // check if url does not have http:// | https:// add it
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'http://$url';
      }
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        debugPrint('Could not launch $url');
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
    }
  }

  Widget _buildInteractionSection(bool isPaidPost, bool hasAccess) {
    return Container(
      padding: EdgeInsets.all(8),
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: Colors.grey[200]!)),
      ),
      child: Row(
        children: [
          // Like button
          _buildActionButton(
            icon: _isLiked ? Icons.favorite : Icons.favorite_border,
            label: '${widget.post.likes}',
            onTap: () {
              _handleLike();
            },
            color: _isLiked ? Colors.red : Colors.grey[600]!,
          ),
          SizedBox(width: widget.isTablet ? 24 : 16),

          // Views
          _buildActionButton(
            icon: Icons.visibility_outlined,
            label: '${widget.post.views}',
            onTap: widget.onView,
            color: Colors.grey[600]!,
            isClickable: false,
          ),

          SizedBox(width: widget.isTablet ? 24 : 16),

          // Share button
          _buildActionButton(
            icon: Icons.share_outlined,
            label: '',
            onTap: () => _handleShare(),
            color: Colors.grey[600]!,
          ),

          const Spacer(),

          // Post price display for paid posts
          if (isPaidPost) ...[
            const SizedBox(width: 12),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: 6,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: hasAccess ? Colors.green : const Color(0xFF5159FF),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    hasAccess ? Icons.check_circle : Icons.attach_money,
                    size: 14,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    hasAccess ? 'OWNED' : widget.post.formattedPrice,
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                      fontSize: widget.isTablet ? 12 : 11,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    VoidCallback? onTap,
    bool isClickable = true,
  }) {
    final child = Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: widget.isTablet ? 20 : 18, color: color),
        if (label.isNotEmpty) ...[
          const SizedBox(width: 6),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w600,
              fontSize: widget.isTablet ? 14 : 12,
            ),
          ),
        ],
      ],
    );

    if (isClickable && onTap != null) {
      return AnimatedContainer(
        duration: const Duration(milliseconds: 150),
        curve: Curves.easeInOut,
        child: GestureDetector(
          onTap: onTap,
          child: AnimatedScale(
            duration: const Duration(milliseconds: 100),
            scale: 1.0,
            child: child,
          ),
        ),
      );
    }

    return child;
  }

  String _getVideoDuration() {
    // For now, return a placeholder duration
    // In a real implementation, you would extract actual video duration
    final random =
        widget.post.id.hashCode % 300; // Generate consistent "duration"
    final minutes = random ~/ 60;
    final seconds = random % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }

  void _openImageGallery(int initialIndex) {
    if (widget.post.imageUrls.isNotEmpty || widget.post.videoUrls.isNotEmpty) {
      HapticFeedback.mediumImpact();
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ImageGalleryScreen(
            imageUrls: widget.post.imageUrls,
            videoUrls: widget.post.videoUrls,
            initialIndex: initialIndex,
            postTitle:
                widget.post.title ?? _getPreviewContent(widget.post.content),
            authorName: widget.post.author,
          ),
        ),
      );
    }
  }

  void _openMediaPlayer({int initialIndex = 0}) {
    if (widget.post.videoUrls.isNotEmpty) {
      HapticFeedback.mediumImpact();
      context.pushSmoothFade(
        MediaPlayerScreen(
          videoUrls: widget.post.videoUrls,
          initialIndex: initialIndex,
          postTitle:
              widget.post.title ?? _getPreviewContent(widget.post.content),
          authorName: widget.post.author,
        ),
      );
    }
  }

  void _handleLike() {
    HapticFeedback.lightImpact();

    // Optimistically update UI
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser != null) {
      setState(() {
        _isLiked = !_isLiked;
      });
    }

    // Call the callback to handle Firebase update
    widget.onLike?.call();
  }

  void _handleShare() {
    HapticFeedback.lightImpact();

    // Get the render box for share position (for iPad/tablet share sheet positioning)
    final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
    Rect? sharePositionOrigin;

    if (renderBox != null) {
      final position = renderBox.localToGlobal(Offset.zero);
      sharePositionOrigin = Rect.fromLTWH(
        position.dx,
        position.dy,
        renderBox.size.width,
        renderBox.size.height,
      );
    }

    // Show share options
    _sharingService.showShareOptions(
      context,
      widget.post,
      sharePositionOrigin: sharePositionOrigin,
    );
  }
}
