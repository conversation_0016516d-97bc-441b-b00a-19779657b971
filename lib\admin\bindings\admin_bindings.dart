import 'package:get/get.dart';
import '../controllers/admin_auth_controller.dart';
import '../controllers/user_management_controller.dart';
import '../controllers/wallet_management_controller.dart';
import '../controllers/user_activity_controller.dart';
import '../services/admin_realtime_service.dart';

class AdminBindings extends Bindings {
  @override
  void dependencies() {
    // Core admin services
    Get.put<AdminRealtimeService>(AdminRealtimeService(), permanent: true);

    // Authentication controller (always needed)
    Get.put<AdminAuthController>(AdminAuthController(), permanent: true);
  }
}

class AdminDashboardBindings extends Bindings {
  @override
  void dependencies() {
    // Ensure core bindings are loaded
    if (!Get.isRegistered<AdminAuthController>()) {
      AdminBindings().dependencies();
    }

    // Dashboard-specific controllers
    Get.lazyPut<UserManagementController>(() => UserManagementController());
    Get.lazyPut<WalletManagementController>(() => WalletManagementController());
    Get.lazyPut<UserActivityController>(() => UserActivityController());
  }
}

class UserManagementBindings extends Bindings {
  @override
  void dependencies() {
    // Ensure core bindings are loaded
    if (!Get.isRegistered<AdminAuthController>()) {
      AdminBindings().dependencies();
    }

    // User management specific (now includes wallet management)
    Get.put<UserManagementController>(UserManagementController());
    Get.put<WalletManagementController>(WalletManagementController());
  }
}

class UserActivityBindings extends Bindings {
  @override
  void dependencies() {
    // Ensure core bindings are loaded
    if (!Get.isRegistered<AdminAuthController>()) {
      AdminBindings().dependencies();
    }

    // User activity specific
    Get.put<UserActivityController>(UserActivityController());
  }
}
