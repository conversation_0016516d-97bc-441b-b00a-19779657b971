# Money Mouthy Admin Panel

A comprehensive admin panel for managing the Money Mouthy application with real-time monitoring, user management, and wallet oversight.

## Features

### 🔐 Authentication & Authorization
- **Role-based Access Control**: Super Admin, Admin, and Moderator roles
- **Permission System**: Granular permissions for different admin functions
- **Secure Login**: Firebase Authentication with admin verification
- **Session Management**: Automatic logout and session validation

### 👥 User Management
- **User Overview**: Real-time user statistics and metrics
- **User Search & Filter**: Search by email, name, or username
- **User Actions**: Block/unblock users with reason tracking
- **Profile Management**: View and edit user profiles
- **Activity Monitoring**: Track user login/logout and activities

### 💰 Wallet Management
- **Wallet Overview**: Total balance, transaction counts, and statistics
- **Wallet Actions**: Block/unblock wallets with admin notes
- **Balance Adjustment**: Add or deduct funds with audit trail
- **Transaction History**: View detailed transaction logs
- **Real-time Updates**: Live wallet balance and transaction monitoring

### 📊 User Activity Dashboard
- **Real-time Monitoring**: Live user activity tracking
- **Activity Types**: Login, logout, posts, wallet transactions, profile updates
- **Search & Filter**: Filter by activity type, date range, and user
- **Online Users**: Real-time count of active users
- **Activity Analytics**: Peak hours and activity patterns

### 🎨 UI/UX Features
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Real-time Updates**: Live data with Firebase listeners
- **Modern Interface**: Material 3 design with custom components
- **Dark/Light Theme**: Adaptive theming support
- **Intuitive Navigation**: Sidebar navigation with role-based menu items

## Architecture

### MVC Pattern
```
lib/admin/
├── models/           # Data models
├── controllers/      # GetX controllers for state management
├── screens/          # UI screens
├── widgets/          # Reusable UI components
├── routes/           # Navigation and routing
├── middleware/       # Authentication middleware
└── utils/            # Utilities and helpers
```

### State Management
- **GetX**: Reactive state management with real-time updates
- **Firebase Listeners**: Real-time data synchronization
- **Caching**: Smart caching for improved performance

### Security
- **Firebase Rules**: Secure Firestore rules for admin collections
- **Role Verification**: Server-side role and permission validation
- **Audit Logging**: All admin actions are logged with timestamps

## Setup Instructions

### 1. Admin Account Creation

Run the admin setup utility to create initial admin accounts:

```dart
import 'package:money_mouthy_two/admin/utils/admin_setup.dart';

// For development
await AdminSetup.setupDevelopmentAdmins();

// For production - create super admin
await AdminSetup.createSuperAdmin(
  email: '<EMAIL>',
  password: 'SecurePassword123!',
  name: 'System Administrator',
);
```

### 2. Firebase Security Rules

The admin panel requires specific Firestore security rules. These are automatically included in `firestore.rules`:

```javascript
// Admin collections
match /admins/{adminId} {
  allow read, write: if request.auth != null && 
                     request.auth.uid == adminId &&
                     isVerified();
}

match /admin_logs/{logId} {
  allow read, write: if request.auth != null && 
                     exists(/databases/$(database)/documents/admins/$(request.auth.uid)) &&
                     isVerified();
}
```

### 3. Navigation

Access the admin panel at:
- Development: `http://localhost:3000/admin/login`
- Production: `https://yourdomain.com/admin/login`

## Usage

### Admin Login
1. Navigate to `/admin/login`
2. Enter admin credentials
3. System verifies admin role and permissions
4. Redirected to dashboard upon successful authentication

### User Management
1. Go to **User Management** from sidebar
2. View user statistics and search/filter users
3. Click on user to view detailed profile
4. Use actions menu to block/unblock or edit users

### Wallet Management
1. Access **Wallet Management** from sidebar
2. View wallet statistics and search by user ID
3. Click on wallet to view transaction history
4. Use actions to block wallets or adjust balances

### Activity Monitoring
1. Open **User Activity** from sidebar
2. Monitor real-time user activities
3. Filter by activity type or date range
4. Track online users and activity patterns

## Permissions

### Available Permissions
- `manage_users`: Create, edit, block/unblock user accounts
- `manage_wallets`: View and manage user wallets and transactions
- `view_analytics`: Access analytics and reporting features
- `manage_admins`: Create and manage other admin accounts
- `system_admin`: Full system administration access

### Role Hierarchy
1. **Super Admin**: All permissions, can manage other admins
2. **Admin**: User and wallet management, analytics access
3. **Moderator**: Limited user management and analytics

## Development

### Adding New Features
1. Create models in `models/`
2. Implement controllers in `controllers/`
3. Build UI in `screens/` and `widgets/`
4. Add routes in `routes/admin_routes.dart`
5. Update permissions if needed

### Testing
```bash
# Run tests
flutter test

# Run admin-specific tests
flutter test test/admin/
```

### Building
```bash
# Development build
flutter run

# Production build
flutter build web --release
```

## Security Considerations

### Best Practices
- Use strong passwords for admin accounts
- Regularly rotate admin credentials
- Monitor admin activity logs
- Implement IP whitelisting for production
- Use HTTPS in production
- Regular security audits

### Audit Trail
All admin actions are logged with:
- Admin ID and name
- Action performed
- Timestamp
- Target user/resource
- Additional metadata

## Troubleshooting

### Common Issues

**Admin can't login**
- Verify admin account exists in `admins` collection
- Check admin status is `active`
- Ensure Firebase rules are deployed

**Permission denied errors**
- Verify admin has required permissions
- Check Firebase security rules
- Ensure admin role is correctly set

**Real-time updates not working**
- Check Firebase connection
- Verify Firestore listeners are active
- Check browser console for errors

### Support
For technical support or questions:
- Check the main app documentation
- Review Firebase console for errors
- Contact the development team

## License
This admin panel is part of the Money Mouthy application and follows the same licensing terms.
