import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../models/admin_user_model.dart';
import '../../services/auth_service.dart';

class AdminAuthController extends GetxController {
  static AdminAuthController get to => Get.find();

  // Get AuthService instance
  AuthService get _authService => Get.find<AuthService>();

  // Reactive variables for error handling
  final _errorMessage = RxnString();

  // Getters that delegate to AuthService
  AdminUserModel? get currentAdmin => _authService.adminUser;
  bool get isLoading => _authService.isLoading;
  bool get isAuthenticated => _authService.isAdmin;
  String? get errorMessage => _errorMessage.value;

  /// Delegate sign in to AuthService
  Future<bool> signIn(String email, String password) async {
    try {
      _errorMessage.value = null;
      final result = await _authService.signIn(email, password);

      if (!result.success) {
        _errorMessage.value = result.errorMessage;
      }

      return result.success && result.isAdmin;
    } catch (e) {
      _errorMessage.value = 'An unexpected error occurred';
      debugPrint('Admin sign in error: $e');
      return false;
    }
  }

  /// Delegate sign out to AuthService
  Future<void> signOut() async {
    try {
      await _authService.signOut();
      _errorMessage.value = null;
    } catch (e) {
      debugPrint('Error signing out: $e');
    }
  }

  // Permission getters
  bool get canManageUsers => currentAdmin?.canManageUsers ?? false;
  bool get canManageWallets => currentAdmin?.canManageWallets ?? false;
  bool get canViewAnalytics => currentAdmin?.canViewAnalytics ?? false;
  bool get canManageAdmins => currentAdmin?.canManageAdmins ?? false;

  void clearError() {
    _errorMessage.value = null;
  }
}
