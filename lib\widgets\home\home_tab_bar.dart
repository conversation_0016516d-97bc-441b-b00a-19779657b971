import 'package:flutter/material.dart';

/// Home Tab Bar Widget
class HomeTabBar extends StatelessWidget {
  final TabController tabController;

  const HomeTabBar({super.key, required this.tabController});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(0),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
      ),
      child: TabBar(
        padding: EdgeInsets.all(0),
        controller: tabController,
        labelColor: Colors.black,
        unselectedLabelColor: Colors.grey,
        indicatorColor: const Color(0xFF4C5DFF),
        // indicatorWeight: 3,
        tabs: const [Tab(text: 'Category'), Tab(text: 'Following')],
      ),
    );
  }
}
