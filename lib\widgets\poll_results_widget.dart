import 'package:flutter/material.dart';

/// A simple pie chart widget for displaying poll results
class PollResultsWidget extends StatelessWidget {
  final Map<String, int> votes;
  final String? userVote;
  final double size;

  const PollResultsWidget({
    super.key,
    required this.votes,
    this.userVote,
    this.size = 100,
  });

  @override
  Widget build(BuildContext context) {
    // Safety check for null or empty votes
    if (votes.isEmpty) {
      return _buildEmptyResults();
    }

    final totalVotes = votes.values.fold(0, (sum, count) => sum + count);
    if (totalVotes == 0) {
      return _buildEmptyResults();
    }

    return _buildCompactResults(totalVotes);
  }

  Widget _buildEmptyResults() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: const Text(
        'No votes yet',
        style: TextStyle(
          fontSize: 12,
          color: Colors.grey,
        ),
      ),
    );
  }

  Widget _buildCompactResults(int totalVotes) {
    final colors = {
      'yes': Colors.green,
      'no': Colors.red,
      'dont_care': Colors.orange,
    };

    final labels = {
      'yes': 'Yes',
      'no': 'No',
      'dont_care': "Don't care",
    };

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          // Line chart representation
          Expanded(
            flex: 3,
            child: Container(
              height: 20,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.grey[200],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: Row(
                  children: _buildLineChartSegments(colors, totalVotes),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          // Compact results
          Expanded(
            flex: 2,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: votes.entries.map((entry) {
                final key = entry.key;
                final count = entry.value;
                final percentage = totalVotes > 0
                    ? (count / totalVotes * 100).toDouble()
                    : 0.0;
                final color = colors[key] ?? Colors.grey;
                final isUserVote = userVote == key;

                return _buildCompactOption(
                  labels[key] ?? key,
                  count,
                  percentage,
                  color,
                  isUserVote,
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildLineChartSegments(
      Map<String, Color> colors, int totalVotes) {
    List<Widget> segments = [];

    for (final entry in votes.entries) {
      final key = entry.key;
      final count = entry.value;
      if (count == 0) continue;

      final percentage = count / totalVotes;
      final color = colors[key] ?? Colors.grey;

      segments.add(
        Expanded(
          flex: (percentage * 100).round(),
          child: Container(
            height: 20,
            color: color.withOpacity(userVote == key ? 1.0 : 0.7),
          ),
        ),
      );
    }

    return segments.isEmpty
        ? [Expanded(child: Container(color: Colors.grey[200]))]
        : segments;
  }

  Widget _buildCompactOption(
    String label,
    int count,
    double percentage,
    Color color,
    bool isUserVote,
  ) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
            border:
                isUserVote ? Border.all(color: Colors.white, width: 2) : null,
            boxShadow: isUserVote
                ? [
                    BoxShadow(
                      color: color.withOpacity(0.5),
                      blurRadius: 4,
                      spreadRadius: 1,
                    )
                  ]
                : null,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          '${percentage.toStringAsFixed(0)}%',
          style: TextStyle(
            fontSize: 10,
            fontWeight: isUserVote ? FontWeight.bold : FontWeight.normal,
            color: isUserVote ? color : Colors.grey[600],
          ),
        ),
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 9,
            color: Colors.grey[500],
          ),
        ),
      ],
    );
  }
}
